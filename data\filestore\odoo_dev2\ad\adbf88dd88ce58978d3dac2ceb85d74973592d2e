

/* ## CSS error message ##*/
body::before {
  font-weight: bold;
  content: "A css error occured, using an old style to render this page";
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 100000000000;
  background-color: #C00;
  color: #DDD;
}

css_error_message {
  content: "Error: Undefined variable: \"$o-white\".\A        on line 19:12 of stdin\A>> $white:    $o-white !default;
\A   -----------^\AThis error occurred while compiling the bundle 'web._assets_backend_helpers' containing:\A    - /web/static/src/scss/bootstrap_overridden.scss\A    - /web/static/src/scss/bs_mixins_overrides_backend.scss\A    - /web_editor/static/src/scss/bootstrap_overridden_backend.scss\A    - /web_editor/static/src/scss/bootstrap_overridden.scss";
}
