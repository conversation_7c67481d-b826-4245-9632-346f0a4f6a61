
/* <inline asset> */
@charset "UTF-8"; 

/* /web/static/lib/bootstrap/scss/_functions.scss */
 

/* /web/static/lib/bootstrap/scss/_mixins.scss */
 

/* /web/static/src/scss/functions.scss */
 

/* /web/static/src/scss/mixins_forwardport.scss */
 

/* /web/static/src/scss/bs_mixins_overrides.scss */
 

/* /web/static/src/scss/utils.scss */
 #oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale, .o_we_cc_preview_wrapper, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page we-button, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="transparent_grayscale"] .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="theme"] .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="reset"] .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="custom"] .o_we_color_btn, .o_colorpicker_widget .o_opacity_slider, .o_colorpicker_widget .o_color_preview{position: relative; z-index: 0;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale::before, .o_we_cc_preview_wrapper::before, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview::before, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page we-button::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="transparent_grayscale"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="theme"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="reset"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="custom"] .o_we_color_btn::before, .o_colorpicker_widget .o_opacity_slider::before, .o_colorpicker_widget .o_color_preview::before{content: ""; position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: -1; background-image: url("/web/static/img/transparent.png"); background-size: 10px auto; border-radius: inherit;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale::after, .o_we_cc_preview_wrapper::after, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview::after, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page we-button::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="transparent_grayscale"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="theme"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="reset"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="custom"] .o_we_color_btn::after, .o_colorpicker_widget .o_opacity_slider::after, .o_colorpicker_widget .o_color_preview::after{content: ""; position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: -1; background: inherit; border-radius: inherit;}

/* /web/static/src/scss/primary_variables.scss */
 

/* /web/static/src/core/avatar/avatar.variables.scss */
 

/* /web/static/src/core/notifications/notification.variables.scss */
 

/* /web/static/src/search/control_panel/control_panel.variables.scss */
 

/* /web/static/src/search/search_panel/search_panel.variables.scss */
 

/* /web/static/src/views/fields/statusbar/statusbar_field.variables.scss */
 

/* /web/static/src/views/form/form.variables.scss */
 

/* /web/static/src/views/kanban/kanban.variables.scss */
 

/* /web/static/src/webclient/burger_menu/burger_menu.variables.scss */
 

/* /web/static/src/webclient/navbar/navbar.variables.scss */
 .o_main_navbar .o_menu_brand, .o_main_navbar .o_navbar_apps_menu .dropdown-toggle, .o_main_navbar .o_nav_entry, .o_main_navbar .dropdown-toggle{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; width: auto; height: calc(var(--o-navbar-height) - 0px); border-radius: 0; user-select: none; background: transparent; font-size: 1em; color: var(--NavBar-entry-color, rgba(255, 255, 255, 0.9));}.o_main_navbar .o_menu_brand:hover, .o_main_navbar .o_nav_entry:hover, .o_main_navbar .dropdown-toggle:hover, .o_main_navbar .o_menu_brand:focus, .o_main_navbar .o_nav_entry:focus, .o_main_navbar .dropdown-toggle:focus, .o_main_navbar .focus.o_menu_brand, .o_main_navbar .focus.o_nav_entry, .o_main_navbar .focus.dropdown-toggle{color: var(--NavBar-entry-color--hover, #FFFFFF);}.o_main_navbar .o_menu_brand, .o_main_navbar .o_nav_entry, .o_main_navbar .dropdown-toggle{margin: 0; margin-left: var(--NavBar-entry-margin-left, 0); margin-right: var(--NavBar-entry-margin-right, 0); padding: 0; padding-left: var(--NavBar-entry-padding-left, 0.63em); padding-right: var(--NavBar-entry-padding-right, 0.63em); line-height: calc(var(--o-navbar-height) - 0px);}

/* /web_editor/static/src/scss/web_editor.variables.scss */
 

/* /web_editor/static/src/scss/wysiwyg.variables.scss */
 

/* /web/static/src/scss/secondary_variables.scss */
 

/* /web_editor/static/src/scss/secondary_variables.scss */
 

/* /web/static/src/scss/pre_variables.scss */
 

/* /web/static/lib/bootstrap/scss/_variables.scss */
 

/* /web/static/src/libs/fontawesome/css/font-awesome.css */
 @font-face{font-family: 'FontAwesome'; src: url('/web/static/src/libs/fontawesome/css/../fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'), url('/web/static/src/libs/fontawesome/css/../fonts/fontawesome-webfont.woff?v=4.7.0') format('woff'); font-weight: normal; font-style: normal; font-display: block;}.fa{display: inline-block; font: normal normal normal 14px/1 FontAwesome; font-size: inherit; text-rendering: auto; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}.fa-lg{font-size: 1.315em; vertical-align: -6%;}.fa-2x{font-size: 2em;}.fa-3x{font-size: 3em;}.fa-4x{font-size: 4em;}.fa-5x{font-size: 5em;}.fa-fw{width: 1.28571429em; text-align: center;}.fa-ul{padding-left: 0; margin-left: 2.14285714em; list-style-type: none;}.fa-ul > li{position: relative;}.fa-li{position: absolute; left: -2.14285714em; width: 2.14285714em; top: 0.14285714em; text-align: center;}.fa-li.fa-lg{left: -1.85714286em;}.fa-border{padding: .2em .25em .15em; border: solid 0.08em #eeeeee; border-radius: .1em;}.fa-pull-left{float: left;}.fa-pull-right{float: right;}.fa.fa-pull-left{margin-right: .3em;}.fa.fa-pull-right{margin-left: .3em;}.fa-spin{animation: fa-spin 2s infinite linear;}.fa-pulse{animation: fa-spin 1s infinite steps(8);}@keyframes fa-spin{0%{transform: rotate(0deg);}100%{transform: rotate(359deg);}}.fa-rotate-90{transform: rotate(90deg);}.fa-rotate-180{transform: rotate(180deg);}.fa-rotate-270{transform: rotate(270deg);}.fa-flip-horizontal{transform: scale(-1, 1);}.fa-flip-vertical{transform: scale(1, -1);}:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical{filter: none;}.fa-stack{position: relative; display: inline-block; width: 2em; height: 2em; line-height: 2em; vertical-align: middle;}.fa-stack-1x, .fa-stack-2x{position: absolute; left: 0; width: 100%; text-align: center;}.fa-stack-1x{line-height: inherit;}.fa-stack-2x{font-size: 2em;}.fa-inverse{color: #ffffff;}.fa-glass:before{content: "\f000";}.fa-music:before{content: "\f001";}.fa-search:before{content: "\f002";}.fa-envelope-o:before{content: "\f003";}.fa-heart:before{content: "\f004";}.fa-star:before{content: "\f005";}.fa-star-o:before{content: "\f006";}.fa-user:before{content: "\f007";}.fa-film:before{content: "\f008";}.fa-th-large:before{content: "\f009";}.fa-th:before{content: "\f00a";}.fa-th-list:before{content: "\f00b";}.fa-check:before{content: "\f00c";}.fa-remove:before, .fa-close:before, .fa-times:before{content: "\f00d";}.fa-search-plus:before{content: "\f00e";}.fa-search-minus:before{content: "\f010";}.fa-power-off:before{content: "\f011";}.fa-signal:before{content: "\f012";}.fa-gear:before, .fa-cog:before{content: "\f013";}.fa-trash-o:before{content: "\f014";}.fa-home:before{content: "\f015";}.fa-file-o:before{content: "\f016";}.fa-clock-o:before{content: "\f017";}.fa-road:before{content: "\f018";}.fa-download:before{content: "\f019";}.fa-arrow-circle-o-down:before{content: "\f01a";}.fa-arrow-circle-o-up:before{content: "\f01b";}.fa-inbox:before{content: "\f01c";}.fa-play-circle-o:before{content: "\f01d";}.fa-rotate-right:before, .fa-repeat:before{content: "\f01e";}.fa-refresh:before{content: "\f021";}.fa-list-alt:before{content: "\f022";}.fa-lock:before{content: "\f023";}.fa-flag:before{content: "\f024";}.fa-headphones:before{content: "\f025";}.fa-volume-off:before{content: "\f026";}.fa-volume-down:before{content: "\f027";}.fa-volume-up:before{content: "\f028";}.fa-qrcode:before{content: "\f029";}.fa-barcode:before{content: "\f02a";}.fa-tag:before{content: "\f02b";}.fa-tags:before{content: "\f02c";}.fa-book:before{content: "\f02d";}.fa-bookmark:before{content: "\f02e";}.fa-print:before{content: "\f02f";}.fa-camera:before{content: "\f030";}.fa-font:before{content: "\f031";}.fa-bold:before{content: "\f032";}.fa-italic:before{content: "\f033";}.fa-text-height:before{content: "\f034";}.fa-text-width:before{content: "\f035";}.fa-align-left:before{content: "\f036";}.fa-align-center:before{content: "\f037";}.fa-align-right:before{content: "\f038";}.fa-align-justify:before{content: "\f039";}.fa-list:before{content: "\f03a";}.fa-dedent:before, .fa-outdent:before{content: "\f03b";}.fa-indent:before{content: "\f03c";}.fa-video-camera:before{content: "\f03d";}.fa-photo:before, .fa-image:before, .fa-picture-o:before{content: "\f03e";}.fa-pencil:before{content: "\f040";}.fa-map-marker:before{content: "\f041";}.fa-adjust:before{content: "\f042";}.fa-tint:before{content: "\f043";}.fa-edit:before, .fa-pencil-square-o:before{content: "\f044";}.fa-share-square-o:before{content: "\f045";}.fa-check-square-o:before{content: "\f046";}.fa-arrows:before{content: "\f047";}.fa-step-backward:before{content: "\f048";}.fa-fast-backward:before{content: "\f049";}.fa-backward:before{content: "\f04a";}.fa-play:before{content: "\f04b";}.fa-pause:before{content: "\f04c";}.fa-stop:before{content: "\f04d";}.fa-forward:before{content: "\f04e";}.fa-fast-forward:before{content: "\f050";}.fa-step-forward:before{content: "\f051";}.fa-eject:before{content: "\f052";}.fa-chevron-left:before{content: "\f053";}.fa-chevron-right:before{content: "\f054";}.fa-plus-circle:before{content: "\f055";}.fa-minus-circle:before{content: "\f056";}.fa-times-circle:before{content: "\f057";}.fa-check-circle:before{content: "\f058";}.fa-question-circle:before{content: "\f059";}.fa-info-circle:before{content: "\f05a";}.fa-crosshairs:before{content: "\f05b";}.fa-times-circle-o:before{content: "\f05c";}.fa-check-circle-o:before{content: "\f05d";}.fa-ban:before{content: "\f05e";}.fa-arrow-left:before{content: "\f060";}.fa-arrow-right:before{content: "\f061";}.fa-arrow-up:before{content: "\f062";}.fa-arrow-down:before{content: "\f063";}.fa-mail-forward:before, .fa-share:before{content: "\f064";}.fa-expand:before{content: "\f065";}.fa-compress:before{content: "\f066";}.fa-plus:before{content: "\f067";}.fa-minus:before{content: "\f068";}.fa-asterisk:before{content: "\f069";}.fa-exclamation-circle:before{content: "\f06a";}.fa-gift:before{content: "\f06b";}.fa-leaf:before{content: "\f06c";}.fa-fire:before{content: "\f06d";}.fa-eye:before{content: "\f06e";}.fa-eye-slash:before{content: "\f070";}.fa-warning:before, .fa-exclamation-triangle:before{content: "\f071";}.fa-plane:before{content: "\f072";}.fa-calendar:before{content: "\f073";}.fa-random:before{content: "\f074";}.fa-comment:before{content: "\f075";}.fa-magnet:before{content: "\f076";}.fa-chevron-up:before{content: "\f077";}.fa-chevron-down:before{content: "\f078";}.fa-retweet:before{content: "\f079";}.fa-shopping-cart:before{content: "\f07a";}.fa-folder:before{content: "\f07b";}.fa-folder-open:before{content: "\f07c";}.fa-arrows-v:before{content: "\f07d";}.fa-arrows-h:before{content: "\f07e";}.fa-bar-chart-o:before, .fa-bar-chart:before{content: "\f080";}.fa-twitter-square:before{content: "\f081";}.fa-facebook-square:before{content: "\f082";}.fa-camera-retro:before{content: "\f083";}.fa-key:before{content: "\f084";}.fa-gears:before, .fa-cogs:before{content: "\f085";}.fa-comments:before{content: "\f086";}.fa-thumbs-o-up:before{content: "\f087";}.fa-thumbs-o-down:before{content: "\f088";}.fa-star-half:before{content: "\f089";}.fa-heart-o:before{content: "\f08a";}.fa-sign-out:before{content: "\f08b";}.fa-linkedin-square:before{content: "\f08c";}.fa-thumb-tack:before{content: "\f08d";}.fa-external-link:before{content: "\f08e";}.fa-sign-in:before{content: "\f090";}.fa-trophy:before{content: "\f091";}.fa-github-square:before{content: "\f092";}.fa-upload:before{content: "\f093";}.fa-lemon-o:before{content: "\f094";}.fa-phone:before{content: "\f095";}.fa-square-o:before{content: "\f096";}.fa-bookmark-o:before{content: "\f097";}.fa-phone-square:before{content: "\f098";}.fa-twitter:before{content: "\f099";}.fa-facebook-f:before, .fa-facebook:before{content: "\f09a";}.fa-github:before{content: "\f09b";}.fa-unlock:before{content: "\f09c";}.fa-credit-card:before{content: "\f09d";}.fa-feed:before, .fa-rss:before{content: "\f09e";}.fa-hdd-o:before{content: "\f0a0";}.fa-bullhorn:before{content: "\f0a1";}.fa-bell:before{content: "\f0f3";}.fa-certificate:before{content: "\f0a3";}.fa-hand-o-right:before{content: "\f0a4";}.fa-hand-o-left:before{content: "\f0a5";}.fa-hand-o-up:before{content: "\f0a6";}.fa-hand-o-down:before{content: "\f0a7";}.fa-arrow-circle-left:before{content: "\f0a8";}.fa-arrow-circle-right:before{content: "\f0a9";}.fa-arrow-circle-up:before{content: "\f0aa";}.fa-arrow-circle-down:before{content: "\f0ab";}.fa-globe:before{content: "\f0ac";}.fa-wrench:before{content: "\f0ad";}.fa-tasks:before{content: "\f0ae";}.fa-filter:before{content: "\f0b0";}.fa-briefcase:before{content: "\f0b1";}.fa-arrows-alt:before{content: "\f0b2";}.fa-group:before, .fa-users:before{content: "\f0c0";}.fa-chain:before, .fa-link:before{content: "\f0c1";}.fa-cloud:before{content: "\f0c2";}.fa-flask:before{content: "\f0c3";}.fa-cut:before, .fa-scissors:before{content: "\f0c4";}.fa-copy:before, .fa-files-o:before{content: "\f0c5";}.fa-paperclip:before{content: "\f0c6";}.fa-save:before, .fa-floppy-o:before{content: "\f0c7";}.fa-square:before{content: "\f0c8";}.fa-navicon:before, .fa-reorder:before, .fa-bars:before{content: "\f0c9";}.fa-list-ul:before{content: "\f0ca";}.fa-list-ol:before{content: "\f0cb";}.fa-strikethrough:before{content: "\f0cc";}.fa-underline:before{content: "\f0cd";}.fa-table:before{content: "\f0ce";}.fa-magic:before{content: "\f0d0";}.fa-truck:before{content: "\f0d1";}.fa-pinterest:before{content: "\f0d2";}.fa-pinterest-square:before{content: "\f0d3";}.fa-google-plus-square:before{content: "\f0d4";}.fa-google-plus:before{content: "\f0d5";}.fa-money:before{content: "\f0d6";}.fa-caret-down:before{content: "\f0d7";}.fa-caret-up:before{content: "\f0d8";}.fa-caret-left:before{content: "\f0d9";}.fa-caret-right:before{content: "\f0da";}.fa-columns:before{content: "\f0db";}.fa-unsorted:before, .fa-sort:before{content: "\f0dc";}.fa-sort-down:before, .fa-sort-desc:before{content: "\f0dd";}.fa-sort-up:before, .fa-sort-asc:before{content: "\f0de";}.fa-envelope:before{content: "\f0e0";}.fa-linkedin:before{content: "\f0e1";}.fa-rotate-left:before, .fa-undo:before{content: "\f0e2";}.fa-legal:before, .fa-gavel:before{content: "\f0e3";}.fa-dashboard:before, .fa-tachometer:before{content: "\f0e4";}.fa-comment-o:before{content: "\f0e5";}.fa-comments-o:before{content: "\f0e6";}.fa-flash:before, .fa-bolt:before{content: "\f0e7";}.fa-sitemap:before{content: "\f0e8";}.fa-umbrella:before{content: "\f0e9";}.fa-paste:before, .fa-clipboard:before{content: "\f0ea";}.fa-lightbulb-o:before{content: "\f0eb";}.fa-exchange:before{content: "\f0ec";}.fa-cloud-download:before{content: "\f0ed";}.fa-cloud-upload:before{content: "\f0ee";}.fa-user-md:before{content: "\f0f0";}.fa-stethoscope:before{content: "\f0f1";}.fa-suitcase:before{content: "\f0f2";}.fa-bell-o:before{content: "\f0a2";}.fa-coffee:before{content: "\f0f4";}.fa-cutlery:before{content: "\f0f5";}.fa-file-text-o:before{content: "\f0f6";}.fa-building-o:before{content: "\f0f7";}.fa-hospital-o:before{content: "\f0f8";}.fa-ambulance:before{content: "\f0f9";}.fa-medkit:before{content: "\f0fa";}.fa-fighter-jet:before{content: "\f0fb";}.fa-beer:before{content: "\f0fc";}.fa-h-square:before{content: "\f0fd";}.fa-plus-square:before{content: "\f0fe";}.fa-angle-double-left:before{content: "\f100";}.fa-angle-double-right:before{content: "\f101";}.fa-angle-double-up:before{content: "\f102";}.fa-angle-double-down:before{content: "\f103";}.fa-angle-left:before{content: "\f104";}.fa-angle-right:before{content: "\f105";}.fa-angle-up:before{content: "\f106";}.fa-angle-down:before{content: "\f107";}.fa-desktop:before{content: "\f108";}.fa-laptop:before{content: "\f109";}.fa-tablet:before{content: "\f10a";}.fa-mobile-phone:before, .fa-mobile:before{content: "\f10b";}.fa-circle-o:before{content: "\f10c";}.fa-quote-left:before{content: "\f10d";}.fa-quote-right:before{content: "\f10e";}.fa-spinner:before{content: "\f110";}.fa-circle:before{content: "\f111";}.fa-mail-reply:before, .fa-reply:before{content: "\f112";}.fa-github-alt:before{content: "\f113";}.fa-folder-o:before{content: "\f114";}.fa-folder-open-o:before{content: "\f115";}.fa-smile-o:before{content: "\f118";}.fa-frown-o:before{content: "\f119";}.fa-meh-o:before{content: "\f11a";}.fa-gamepad:before{content: "\f11b";}.fa-keyboard-o:before{content: "\f11c";}.fa-flag-o:before{content: "\f11d";}.fa-flag-checkered:before{content: "\f11e";}.fa-terminal:before{content: "\f120";}.fa-code:before{content: "\f121";}.fa-mail-reply-all:before, .fa-reply-all:before{content: "\f122";}.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before{content: "\f123";}.fa-location-arrow:before{content: "\f124";}.fa-crop:before{content: "\f125";}.fa-code-fork:before{content: "\f126";}.fa-unlink:before, .fa-chain-broken:before{content: "\f127";}.fa-question:before{content: "\f128";}.fa-info:before{content: "\f129";}.fa-exclamation:before{content: "\f12a";}.fa-superscript:before{content: "\f12b";}.fa-subscript:before{content: "\f12c";}.fa-eraser:before{content: "\f12d";}.fa-puzzle-piece:before{content: "\f12e";}.fa-microphone:before{content: "\f130";}.fa-microphone-slash:before{content: "\f131";}.fa-shield:before{content: "\f132";}.fa-calendar-o:before{content: "\f133";}.fa-fire-extinguisher:before{content: "\f134";}.fa-rocket:before{content: "\f135";}.fa-maxcdn:before{content: "\f136";}.fa-chevron-circle-left:before{content: "\f137";}.fa-chevron-circle-right:before{content: "\f138";}.fa-chevron-circle-up:before{content: "\f139";}.fa-chevron-circle-down:before{content: "\f13a";}.fa-html5:before{content: "\f13b";}.fa-css3:before{content: "\f13c";}.fa-anchor:before{content: "\f13d";}.fa-unlock-alt:before{content: "\f13e";}.fa-bullseye:before{content: "\f140";}.fa-ellipsis-h:before{content: "\f141";}.fa-ellipsis-v:before{content: "\f142";}.fa-rss-square:before{content: "\f143";}.fa-play-circle:before{content: "\f144";}.fa-ticket:before{content: "\f145";}.fa-minus-square:before{content: "\f146";}.fa-minus-square-o:before{content: "\f147";}.fa-level-up:before{content: "\f148";}.fa-level-down:before{content: "\f149";}.fa-check-square:before{content: "\f14a";}.fa-pencil-square:before{content: "\f14b";}.fa-external-link-square:before{content: "\f14c";}.fa-share-square:before{content: "\f14d";}.fa-compass:before{content: "\f14e";}.fa-toggle-down:before, .fa-caret-square-o-down:before{content: "\f150";}.fa-toggle-up:before, .fa-caret-square-o-up:before{content: "\f151";}.fa-toggle-right:before, .fa-caret-square-o-right:before{content: "\f152";}.fa-euro:before, .fa-eur:before{content: "\f153";}.fa-gbp:before{content: "\f154";}.fa-dollar:before, .fa-usd:before{content: "\f155";}.fa-rupee:before, .fa-inr:before{content: "\f156";}.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before{content: "\f157";}.fa-ruble:before, .fa-rouble:before, .fa-rub:before{content: "\f158";}.fa-won:before, .fa-krw:before{content: "\f159";}.fa-bitcoin:before, .fa-btc:before{content: "\f15a";}.fa-file:before{content: "\f15b";}.fa-file-text:before{content: "\f15c";}.fa-sort-alpha-asc:before{content: "\f15d";}.fa-sort-alpha-desc:before{content: "\f15e";}.fa-sort-amount-asc:before{content: "\f160";}.fa-sort-amount-desc:before{content: "\f161";}.fa-sort-numeric-asc:before{content: "\f162";}.fa-sort-numeric-desc:before{content: "\f163";}.fa-thumbs-up:before{content: "\f164";}.fa-thumbs-down:before{content: "\f165";}.fa-youtube-square:before{content: "\f166";}.fa-youtube:before{content: "\f167";}.fa-xing:before{content: "\f168";}.fa-xing-square:before{content: "\f169";}.fa-youtube-play:before{content: "\f16a";}.fa-dropbox:before{content: "\f16b";}.fa-stack-overflow:before{content: "\f16c";}.fa-instagram:before{content: "\f16d";}.fa-flickr:before{content: "\f16e";}.fa-adn:before{content: "\f170";}.fa-bitbucket:before{content: "\f171";}.fa-bitbucket-square:before{content: "\f172";}.fa-tumblr:before{content: "\f173";}.fa-tumblr-square:before{content: "\f174";}.fa-long-arrow-down:before{content: "\f175";}.fa-long-arrow-up:before{content: "\f176";}.fa-long-arrow-left:before{content: "\f177";}.fa-long-arrow-right:before{content: "\f178";}.fa-apple:before{content: "\f179";}.fa-windows:before{content: "\f17a";}.fa-android:before{content: "\f17b";}.fa-linux:before{content: "\f17c";}.fa-dribbble:before{content: "\f17d";}.fa-skype:before{content: "\f17e";}.fa-foursquare:before{content: "\f180";}.fa-trello:before{content: "\f181";}.fa-female:before{content: "\f182";}.fa-male:before{content: "\f183";}.fa-gittip:before, .fa-gratipay:before{content: "\f184";}.fa-sun-o:before{content: "\f185";}.fa-moon-o:before{content: "\f186";}.fa-archive:before{content: "\f187";}.fa-bug:before{content: "\f188";}.fa-vk:before{content: "\f189";}.fa-weibo:before{content: "\f18a";}.fa-renren:before{content: "\f18b";}.fa-pagelines:before{content: "\f18c";}.fa-stack-exchange:before{content: "\f18d";}.fa-arrow-circle-o-right:before{content: "\f18e";}.fa-arrow-circle-o-left:before{content: "\f190";}.fa-toggle-left:before, .fa-caret-square-o-left:before{content: "\f191";}.fa-dot-circle-o:before{content: "\f192";}.fa-wheelchair:before{content: "\f193";}.fa-vimeo-square:before{content: "\f194";}.fa-turkish-lira:before, .fa-try:before{content: "\f195";}.fa-plus-square-o:before{content: "\f196";}.fa-space-shuttle:before{content: "\f197";}.fa-slack:before{content: "\f198";}.fa-envelope-square:before{content: "\f199";}.fa-wordpress:before{content: "\f19a";}.fa-openid:before{content: "\f19b";}.fa-institution:before, .fa-bank:before, .fa-university:before{content: "\f19c";}.fa-mortar-board:before, .fa-graduation-cap:before{content: "\f19d";}.fa-yahoo:before{content: "\f19e";}.fa-google:before{content: "\f1a0";}.fa-reddit:before{content: "\f1a1";}.fa-reddit-square:before{content: "\f1a2";}.fa-stumbleupon-circle:before{content: "\f1a3";}.fa-stumbleupon:before{content: "\f1a4";}.fa-delicious:before{content: "\f1a5";}.fa-digg:before{content: "\f1a6";}.fa-pied-piper-pp:before{content: "\f1a7";}.fa-pied-piper-alt:before{content: "\f1a8";}.fa-drupal:before{content: "\f1a9";}.fa-joomla:before{content: "\f1aa";}.fa-language:before{content: "\f1ab";}.fa-fax:before{content: "\f1ac";}.fa-building:before{content: "\f1ad";}.fa-child:before{content: "\f1ae";}.fa-paw:before{content: "\f1b0";}.fa-spoon:before{content: "\f1b1";}.fa-cube:before{content: "\f1b2";}.fa-cubes:before{content: "\f1b3";}.fa-behance:before{content: "\f1b4";}.fa-behance-square:before{content: "\f1b5";}.fa-steam:before{content: "\f1b6";}.fa-steam-square:before{content: "\f1b7";}.fa-recycle:before{content: "\f1b8";}.fa-automobile:before, .fa-car:before{content: "\f1b9";}.fa-cab:before, .fa-taxi:before{content: "\f1ba";}.fa-tree:before{content: "\f1bb";}.fa-spotify:before{content: "\f1bc";}.fa-deviantart:before{content: "\f1bd";}.fa-soundcloud:before{content: "\f1be";}.fa-database:before{content: "\f1c0";}.fa-file-pdf-o:before{content: "\f1c1";}.fa-file-word-o:before{content: "\f1c2";}.fa-file-excel-o:before{content: "\f1c3";}.fa-file-powerpoint-o:before{content: "\f1c4";}.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before{content: "\f1c5";}.fa-file-zip-o:before, .fa-file-archive-o:before{content: "\f1c6";}.fa-file-sound-o:before, .fa-file-audio-o:before{content: "\f1c7";}.fa-file-movie-o:before, .fa-file-video-o:before{content: "\f1c8";}.fa-file-code-o:before{content: "\f1c9";}.fa-vine:before{content: "\f1ca";}.fa-codepen:before{content: "\f1cb";}.fa-jsfiddle:before{content: "\f1cc";}.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before{content: "\f1cd";}.fa-circle-o-notch:before{content: "\f1ce";}.fa-ra:before, .fa-resistance:before, .fa-rebel:before{content: "\f1d0";}.fa-ge:before, .fa-empire:before{content: "\f1d1";}.fa-git-square:before{content: "\f1d2";}.fa-git:before{content: "\f1d3";}.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before{content: "\f1d4";}.fa-tencent-weibo:before{content: "\f1d5";}.fa-qq:before{content: "\f1d6";}.fa-wechat:before, .fa-weixin:before{content: "\f1d7";}.fa-send:before, .fa-paper-plane:before{content: "\f1d8";}.fa-send-o:before, .fa-paper-plane-o:before{content: "\f1d9";}.fa-history:before{content: "\f1da";}.fa-circle-thin:before{content: "\f1db";}.fa-header:before{content: "\f1dc";}.fa-paragraph:before{content: "\f1dd";}.fa-sliders:before{content: "\f1de";}.fa-share-alt:before{content: "\f1e0";}.fa-share-alt-square:before{content: "\f1e1";}.fa-bomb:before{content: "\f1e2";}.fa-soccer-ball-o:before, .fa-futbol-o:before{content: "\f1e3";}.fa-tty:before{content: "\f1e4";}.fa-binoculars:before{content: "\f1e5";}.fa-plug:before{content: "\f1e6";}.fa-slideshare:before{content: "\f1e7";}.fa-twitch:before{content: "\f1e8";}.fa-yelp:before{content: "\f1e9";}.fa-newspaper-o:before{content: "\f1ea";}.fa-wifi:before{content: "\f1eb";}.fa-calculator:before{content: "\f1ec";}.fa-paypal:before{content: "\f1ed";}.fa-google-wallet:before{content: "\f1ee";}.fa-cc-visa:before{content: "\f1f0";}.fa-cc-mastercard:before{content: "\f1f1";}.fa-cc-discover:before{content: "\f1f2";}.fa-cc-amex:before{content: "\f1f3";}.fa-cc-paypal:before{content: "\f1f4";}.fa-cc-stripe:before{content: "\f1f5";}.fa-bell-slash:before{content: "\f1f6";}.fa-bell-slash-o:before{content: "\f1f7";}.fa-trash:before{content: "\f1f8";}.fa-copyright:before{content: "\f1f9";}.fa-at:before{content: "\f1fa";}.fa-eyedropper:before{content: "\f1fb";}.fa-paint-brush:before{content: "\f1fc";}.fa-birthday-cake:before{content: "\f1fd";}.fa-area-chart:before{content: "\f1fe";}.fa-pie-chart:before{content: "\f200";}.fa-line-chart:before{content: "\f201";}.fa-lastfm:before{content: "\f202";}.fa-lastfm-square:before{content: "\f203";}.fa-toggle-off:before{content: "\f204";}.fa-toggle-on:before{content: "\f205";}.fa-bicycle:before{content: "\f206";}.fa-bus:before{content: "\f207";}.fa-ioxhost:before{content: "\f208";}.fa-angellist:before{content: "\f209";}.fa-cc:before{content: "\f20a";}.fa-shekel:before, .fa-sheqel:before, .fa-ils:before{content: "\f20b";}.fa-meanpath:before{content: "\f20c";}.fa-buysellads:before{content: "\f20d";}.fa-connectdevelop:before{content: "\f20e";}.fa-dashcube:before{content: "\f210";}.fa-forumbee:before{content: "\f211";}.fa-leanpub:before{content: "\f212";}.fa-sellsy:before{content: "\f213";}.fa-shirtsinbulk:before{content: "\f214";}.fa-simplybuilt:before{content: "\f215";}.fa-skyatlas:before{content: "\f216";}.fa-cart-plus:before{content: "\f217";}.fa-cart-arrow-down:before{content: "\f218";}.fa-diamond:before{content: "\f219";}.fa-ship:before{content: "\f21a";}.fa-user-secret:before{content: "\f21b";}.fa-motorcycle:before{content: "\f21c";}.fa-street-view:before{content: "\f21d";}.fa-heartbeat:before{content: "\f21e";}.fa-venus:before{content: "\f221";}.fa-mars:before{content: "\f222";}.fa-mercury:before{content: "\f223";}.fa-intersex:before, .fa-transgender:before{content: "\f224";}.fa-transgender-alt:before{content: "\f225";}.fa-venus-double:before{content: "\f226";}.fa-mars-double:before{content: "\f227";}.fa-venus-mars:before{content: "\f228";}.fa-mars-stroke:before{content: "\f229";}.fa-mars-stroke-v:before{content: "\f22a";}.fa-mars-stroke-h:before{content: "\f22b";}.fa-neuter:before{content: "\f22c";}.fa-genderless:before{content: "\f22d";}.fa-facebook-official:before{content: "\f230";}.fa-pinterest-p:before{content: "\f231";}.fa-whatsapp:before{content: "\f232";}.fa-server:before{content: "\f233";}.fa-user-plus:before{content: "\f234";}.fa-user-times:before{content: "\f235";}.fa-hotel:before, .fa-bed:before{content: "\f236";}.fa-viacoin:before{content: "\f237";}.fa-train:before{content: "\f238";}.fa-subway:before{content: "\f239";}.fa-medium:before{content: "\f23a";}.fa-yc:before, .fa-y-combinator:before{content: "\f23b";}.fa-optin-monster:before{content: "\f23c";}.fa-opencart:before{content: "\f23d";}.fa-expeditedssl:before{content: "\f23e";}.fa-battery-4:before, .fa-battery:before, .fa-battery-full:before{content: "\f240";}.fa-battery-3:before, .fa-battery-three-quarters:before{content: "\f241";}.fa-battery-2:before, .fa-battery-half:before{content: "\f242";}.fa-battery-1:before, .fa-battery-quarter:before{content: "\f243";}.fa-battery-0:before, .fa-battery-empty:before{content: "\f244";}.fa-mouse-pointer:before{content: "\f245";}.fa-i-cursor:before{content: "\f246";}.fa-object-group:before{content: "\f247";}.fa-object-ungroup:before{content: "\f248";}.fa-sticky-note:before{content: "\f249";}.fa-sticky-note-o:before{content: "\f24a";}.fa-cc-jcb:before{content: "\f24b";}.fa-cc-diners-club:before{content: "\f24c";}.fa-clone:before{content: "\f24d";}.fa-balance-scale:before{content: "\f24e";}.fa-hourglass-o:before{content: "\f250";}.fa-hourglass-1:before, .fa-hourglass-start:before{content: "\f251";}.fa-hourglass-2:before, .fa-hourglass-half:before{content: "\f252";}.fa-hourglass-3:before, .fa-hourglass-end:before{content: "\f253";}.fa-hourglass:before{content: "\f254";}.fa-hand-grab-o:before, .fa-hand-rock-o:before{content: "\f255";}.fa-hand-stop-o:before, .fa-hand-paper-o:before{content: "\f256";}.fa-hand-scissors-o:before{content: "\f257";}.fa-hand-lizard-o:before{content: "\f258";}.fa-hand-spock-o:before{content: "\f259";}.fa-hand-pointer-o:before{content: "\f25a";}.fa-hand-peace-o:before{content: "\f25b";}.fa-trademark:before{content: "\f25c";}.fa-registered:before{content: "\f25d";}.fa-creative-commons:before{content: "\f25e";}.fa-gg:before{content: "\f260";}.fa-gg-circle:before{content: "\f261";}.fa-tripadvisor:before{content: "\f262";}.fa-odnoklassniki:before{content: "\f263";}.fa-odnoklassniki-square:before{content: "\f264";}.fa-get-pocket:before{content: "\f265";}.fa-wikipedia-w:before{content: "\f266";}.fa-safari:before{content: "\f267";}.fa-chrome:before{content: "\f268";}.fa-firefox:before{content: "\f269";}.fa-opera:before{content: "\f26a";}.fa-internet-explorer:before{content: "\f26b";}.fa-tv:before, .fa-television:before{content: "\f26c";}.fa-contao:before{content: "\f26d";}.fa-500px:before{content: "\f26e";}.fa-amazon:before{content: "\f270";}.fa-calendar-plus-o:before{content: "\f271";}.fa-calendar-minus-o:before{content: "\f272";}.fa-calendar-times-o:before{content: "\f273";}.fa-calendar-check-o:before{content: "\f274";}.fa-industry:before{content: "\f275";}.fa-map-pin:before{content: "\f276";}.fa-map-signs:before{content: "\f277";}.fa-map-o:before{content: "\f278";}.fa-map:before{content: "\f279";}.fa-commenting:before{content: "\f27a";}.fa-commenting-o:before{content: "\f27b";}.fa-houzz:before{content: "\f27c";}.fa-vimeo:before{content: "\f27d";}.fa-black-tie:before{content: "\f27e";}.fa-fonticons:before{content: "\f280";}.fa-reddit-alien:before{content: "\f281";}.fa-edge:before{content: "\f282";}.fa-credit-card-alt:before{content: "\f283";}.fa-codiepie:before{content: "\f284";}.fa-modx:before{content: "\f285";}.fa-fort-awesome:before{content: "\f286";}.fa-usb:before{content: "\f287";}.fa-product-hunt:before{content: "\f288";}.fa-mixcloud:before{content: "\f289";}.fa-scribd:before{content: "\f28a";}.fa-pause-circle:before{content: "\f28b";}.fa-pause-circle-o:before{content: "\f28c";}.fa-stop-circle:before{content: "\f28d";}.fa-stop-circle-o:before{content: "\f28e";}.fa-shopping-bag:before{content: "\f290";}.fa-shopping-basket:before{content: "\f291";}.fa-hashtag:before{content: "\f292";}.fa-bluetooth:before{content: "\f293";}.fa-bluetooth-b:before{content: "\f294";}.fa-percent:before{content: "\f295";}.fa-gitlab:before{content: "\f296";}.fa-wpbeginner:before{content: "\f297";}.fa-wpforms:before{content: "\f298";}.fa-envira:before{content: "\f299";}.fa-universal-access:before{content: "\f29a";}.fa-wheelchair-alt:before{content: "\f29b";}.fa-question-circle-o:before{content: "\f29c";}.fa-blind:before{content: "\f29d";}.fa-audio-description:before{content: "\f29e";}.fa-volume-control-phone:before{content: "\f2a0";}.fa-braille:before{content: "\f2a1";}.fa-assistive-listening-systems:before{content: "\f2a2";}.fa-asl-interpreting:before, .fa-american-sign-language-interpreting:before{content: "\f2a3";}.fa-deafness:before, .fa-hard-of-hearing:before, .fa-deaf:before{content: "\f2a4";}.fa-glide:before{content: "\f2a5";}.fa-glide-g:before{content: "\f2a6";}.fa-signing:before, .fa-sign-language:before{content: "\f2a7";}.fa-low-vision:before{content: "\f2a8";}.fa-viadeo:before{content: "\f2a9";}.fa-viadeo-square:before{content: "\f2aa";}.fa-snapchat:before{content: "\f2ab";}.fa-snapchat-ghost:before{content: "\f2ac";}.fa-snapchat-square:before{content: "\f2ad";}.fa-pied-piper:before{content: "\f2ae";}.fa-first-order:before{content: "\f2b0";}.fa-yoast:before{content: "\f2b1";}.fa-themeisle:before{content: "\f2b2";}.fa-google-plus-circle:before, .fa-google-plus-official:before{content: "\f2b3";}.fa-fa:before, .fa-font-awesome:before{content: "\f2b4";}.fa-handshake-o:before{content: "\f2b5";}.fa-envelope-open:before{content: "\f2b6";}.fa-envelope-open-o:before{content: "\f2b7";}.fa-linode:before{content: "\f2b8";}.fa-address-book:before{content: "\f2b9";}.fa-address-book-o:before{content: "\f2ba";}.fa-vcard:before, .fa-address-card:before{content: "\f2bb";}.fa-vcard-o:before, .fa-address-card-o:before{content: "\f2bc";}.fa-user-circle:before{content: "\f2bd";}.fa-user-circle-o:before{content: "\f2be";}.fa-user-o:before{content: "\f2c0";}.fa-id-badge:before{content: "\f2c1";}.fa-drivers-license:before, .fa-id-card:before{content: "\f2c2";}.fa-drivers-license-o:before, .fa-id-card-o:before{content: "\f2c3";}.fa-quora:before{content: "\f2c4";}.fa-free-code-camp:before{content: "\f2c5";}.fa-telegram:before{content: "\f2c6";}.fa-thermometer-4:before, .fa-thermometer:before, .fa-thermometer-full:before{content: "\f2c7";}.fa-thermometer-3:before, .fa-thermometer-three-quarters:before{content: "\f2c8";}.fa-thermometer-2:before, .fa-thermometer-half:before{content: "\f2c9";}.fa-thermometer-1:before, .fa-thermometer-quarter:before{content: "\f2ca";}.fa-thermometer-0:before, .fa-thermometer-empty:before{content: "\f2cb";}.fa-shower:before{content: "\f2cc";}.fa-bathtub:before, .fa-s15:before, .fa-bath:before{content: "\f2cd";}.fa-podcast:before{content: "\f2ce";}.fa-window-maximize:before{content: "\f2d0";}.fa-window-minimize:before{content: "\f2d1";}.fa-window-restore:before{content: "\f2d2";}.fa-times-rectangle:before, .fa-window-close:before{content: "\f2d3";}.fa-times-rectangle-o:before, .fa-window-close-o:before{content: "\f2d4";}.fa-bandcamp:before{content: "\f2d5";}.fa-grav:before{content: "\f2d6";}.fa-etsy:before{content: "\f2d7";}.fa-imdb:before{content: "\f2d8";}.fa-ravelry:before{content: "\f2d9";}.fa-eercast:before{content: "\f2da";}.fa-microchip:before{content: "\f2db";}.fa-snowflake-o:before{content: "\f2dc";}.fa-superpowers:before{content: "\f2dd";}.fa-wpexplorer:before{content: "\f2de";}.fa-meetup:before{content: "\f2e0";}.visually-hidden{position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); border: 0;}.visually-hidden-focusable:active, .visually-hidden-focusable:focus{position: static; width: auto; height: auto; margin: 0; overflow: visible; clip: auto;}

/* /web/static/lib/odoo_ui_icons/style.css */
@font-face{font-family: 'odoo_ui_icons'; src: url('/web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2') format('woff2'), url('/web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff') format('woff'); font-weight: normal; font-style: normal; font-display: block;}.oi{display: inline-block; font-family: 'odoo_ui_icons'; speak: never; font-style: normal; font-weight: normal; font-variant: normal; text-transform: none; line-height: 1; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}.oi-view-pivot:before{content: '\e800';}.oi-text-break:before{content: '\e801';}.oi-text-inline:before{content: '\e802';}.oi-voip:before{content: '\e803';}.oi-odoo:before{content: '\e806';}.oi-search:before{content: '\e808';}.oi-group:before{content: '\e80a';}.oi-settings-adjust:before{content: '\e80c';}.oi-apps:before{content: '\e80d';}.oi-panel-right:before{content: '\e810';}.oi-launch:before{content: '\e812';}.oi-studio:before{content: '\e813';}.oi-view-kanban:before{content: '\e814';}.oi-text-wrap:before{content: '\e815';}.oi-view-cohort:before{content: '\e816';}.oi-view-list:before{content: '\e817';}.oi-gif-picker:before{content: '\e82e';}.oi-chevron-down:before{content: '\e839';}.oi-chevron-left:before{content: '\e83a';}.oi-chevron-right:before{content: '\e83b';}.oi-chevron-up:before{content: '\e83c';}.oi-arrows-h:before{content: '\e83d';}.oi-arrows-v:before{content: '\e83e';}.oi-arrow-down-left:before{content: '\e83f';}.oi-arrow-down-right:before{content: '\e840';}.oi-arrow-down:before{content: '\e841';}.oi-arrow-left:before{content: '\e842';}.oi-arrow-right:before{content: '\e843';}.oi-arrow-up-left:before{content: '\e844';}.oi-arrow-up-right:before{content: '\e845';}.oi-arrow-up:before{content: '\e846';}.oi-draggable:before{content: '\e847';}.oi-view:before{content: '\e861';}.oi-archive:before{content: '\e862';}.oi-unarchive:before{content: '\e863';}.oi-text-effect:before{content: '\e827';}.oi-smile-add:before{content: '\e84e';}.oi-close:before{content: '\e852';}.o_rtl .oi-chevron-left, .o_rtl .oi-chevron-right, .o_rtl .oi-arrow-down-left, .o_rtl .oi-arrow-down-right, .o_rtl .oi-arrow-left, .o_rtl .oi-arrow-right, .o_rtl .oi-arrow-up-left, .o_rtl .oi-arrow-up-right{transform: rotate(180deg);}

/* /web/static/lib/select2/select2.css */
 .select2-container{margin: 0; position: relative; display: inline-block; vertical-align: middle;}.select2-container, .select2-drop, .select2-search, .select2-search input{-webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box;}.select2-container .select2-choice{display: block; height: 26px; padding: 0 0 0 8px; overflow: hidden; position: relative; border: 1px solid #aaa; white-space: nowrap; line-height: 26px; color: #444; text-decoration: none; border-radius: 4px; background-clip: padding-box; -webkit-touch-callout: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; background-color: #fff; background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eee), color-stop(0.5, #fff)); background-image: -webkit-linear-gradient(center bottom, #eee 0%, #fff 50%); background-image: -moz-linear-gradient(center bottom, #eee 0%, #fff 50%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr = '#ffffff', endColorstr = '#eeeeee', GradientType = 0); background-image: linear-gradient(to top, #eee 0%, #fff 50%);}html[dir="rtl"] .select2-container .select2-choice{padding: 0 8px 0 0;}.select2-container.select2-drop-above .select2-choice{border-bottom-color: #aaa; border-radius: 0 0 4px 4px; background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eee), color-stop(0.9, #fff)); background-image: -webkit-linear-gradient(center bottom, #eee 0%, #fff 90%); background-image: -moz-linear-gradient(center bottom, #eee 0%, #fff 90%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#eeeeee', GradientType=0); background-image: linear-gradient(to bottom, #eee 0%, #fff 90%);}.select2-container.select2-allowclear .select2-choice .select2-chosen{margin-right: 42px;}.select2-container .select2-choice > .select2-chosen{margin-right: 26px; display: block; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; float: none; width: auto;}html[dir="rtl"] .select2-container .select2-choice > .select2-chosen{margin-left: 26px; margin-right: 0;}.select2-container .select2-choice abbr{display: none; width: 12px; height: 12px; position: absolute; right: 24px; top: 8px; font-size: 1px; text-decoration: none; border: 0; background: url('/web/static/lib/select2/select2.png') right top no-repeat; cursor: pointer; outline: 0;}.select2-container.select2-allowclear .select2-choice abbr{display: inline-block;}.select2-container .select2-choice abbr:hover{background-position: right -11px; cursor: pointer;}.select2-drop-mask{border: 0; margin: 0; padding: 0; position: fixed; left: 0; top: 0; min-height: 100%; min-width: 100%; height: auto; width: auto; opacity: 0; z-index: 9998; background-color: #fff; filter: alpha(opacity=0);}.select2-drop{width: 100%; margin-top: -1px; position: absolute; z-index: 9999; top: 100%; background: #fff; color: #000; border: 1px solid #aaa; border-top: 0; border-radius: 0 0 4px 4px; -webkit-box-shadow: 0 4px 5px rgba(0, 0, 0, .15); box-shadow: 0 4px 5px rgba(0, 0, 0, .15);}.select2-drop.select2-drop-above{margin-top: 1px; border-top: 1px solid #aaa; border-bottom: 0; border-radius: 4px 4px 0 0; -webkit-box-shadow: 0 -4px 5px rgba(0, 0, 0, .15); box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);}.select2-drop-active{border: 1px solid #5897fb; border-top: none;}.select2-drop.select2-drop-above.select2-drop-active{border-top: 1px solid #5897fb;}.select2-drop-auto-width{border-top: 1px solid #aaa; width: auto;}.select2-container .select2-choice .select2-arrow{display: inline-block; width: 18px; height: 100%; position: absolute; right: 0; top: 0; border-left: 1px solid #aaa; border-radius: 0 4px 4px 0; background-clip: padding-box; background: #ccc; background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #ccc), color-stop(0.6, #eee)); background-image: -webkit-linear-gradient(center bottom, #ccc 0%, #eee 60%); background-image: -moz-linear-gradient(center bottom, #ccc 0%, #eee 60%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr = '#eeeeee', endColorstr = '#cccccc', GradientType = 0); background-image: linear-gradient(to top, #ccc 0%, #eee 60%);}html[dir="rtl"] .select2-container .select2-choice .select2-arrow{left: 0; right: auto; border-left: none; border-right: 1px solid #aaa; border-radius: 4px 0 0 4px;}.select2-container .select2-choice .select2-arrow b{display: block; width: 100%; height: 100%; background: url('/web/static/lib/select2/select2.png') no-repeat 0 1px;}html[dir="rtl"] .select2-container .select2-choice .select2-arrow b{background-position: 2px 1px;}.select2-search{display: inline-block; width: 100%; min-height: 26px; margin: 0; padding: 4px 4px 0 4px; position: relative; z-index: 10000; white-space: nowrap;}.select2-search input{width: 100%; height: auto !important; min-height: 26px; padding: 4px 20px 4px 5px; margin: 0; outline: 0; font-family: sans-serif; font-size: 1em; border: 1px solid #aaa; border-radius: 0; -webkit-box-shadow: none; box-shadow: none; background: #fff url('/web/static/lib/select2/select2.png') no-repeat 100% -22px; background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, #fff), color-stop(0.99, #eee)); background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, -webkit-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, -moz-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, linear-gradient(to bottom, #fff 85%, #eee 99%) 0 0;}html[dir="rtl"] .select2-search input{padding: 4px 5px 4px 20px; background: #fff url('/web/static/lib/select2/select2.png') no-repeat -37px -22px; background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, #fff), color-stop(0.99, #eee)); background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, -webkit-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, -moz-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, linear-gradient(to bottom, #fff 85%, #eee 99%) 0 0;}.select2-search input.select2-active{background: #fff url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%; background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, #fff), color-stop(0.99, #eee)); background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, -webkit-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, -moz-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, linear-gradient(to bottom, #fff 85%, #eee 99%) 0 0;}.select2-container-active .select2-choice, .select2-container-active .select2-choices{border: 1px solid #5897fb; outline: none; -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3); box-shadow: 0 0 5px rgba(0, 0, 0, .3);}.select2-dropdown-open .select2-choice{border-bottom-color: transparent; -webkit-box-shadow: 0 1px 0 #fff inset; box-shadow: 0 1px 0 #fff inset; border-bottom-left-radius: 0; border-bottom-right-radius: 0; background-color: #eee; background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #fff), color-stop(0.5, #eee)); background-image: -webkit-linear-gradient(center bottom, #fff 0%, #eee 50%); background-image: -moz-linear-gradient(center bottom, #fff 0%, #eee 50%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#ffffff', GradientType=0); background-image: linear-gradient(to top, #fff 0%, #eee 50%);}.select2-dropdown-open.select2-drop-above .select2-choice, .select2-dropdown-open.select2-drop-above .select2-choices{border: 1px solid #5897fb; border-top-color: transparent; background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #fff), color-stop(0.5, #eee)); background-image: -webkit-linear-gradient(center top, #fff 0%, #eee 50%); background-image: -moz-linear-gradient(center top, #fff 0%, #eee 50%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#ffffff', GradientType=0); background-image: linear-gradient(to bottom, #fff 0%, #eee 50%);}.select2-dropdown-open .select2-choice .select2-arrow{background: transparent; border-left: none; filter: none;}html[dir="rtl"] .select2-dropdown-open .select2-choice .select2-arrow{border-right: none;}.select2-dropdown-open .select2-choice .select2-arrow b{background-position: -18px 1px;}html[dir="rtl"] .select2-dropdown-open .select2-choice .select2-arrow b{background-position: -16px 1px;}.select2-hidden-accessible{border: 0; clip: rect(0 0 0 0); height: 1px; margin: -1px; overflow: hidden; padding: 0; position: absolute; width: 1px;}.select2-results{max-height: 200px; padding: 0 0 0 4px; margin: 4px 4px 4px 0; position: relative; overflow-x: hidden; overflow-y: auto; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);}html[dir="rtl"] .select2-results{padding: 0 4px 0 0; margin: 4px 0 4px 4px;}.select2-results ul.select2-result-sub{margin: 0; padding-left: 0;}.select2-results li{list-style: none; display: list-item; background-image: none;}.select2-results li.select2-result-with-children > .select2-result-label{font-weight: bold;}.select2-results .select2-result-label{padding: 3px 7px 4px; margin: 0; cursor: pointer; min-height: 1em; -webkit-touch-callout: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;}.select2-results-dept-1 .select2-result-label{padding-left: 20px}.select2-results-dept-2 .select2-result-label{padding-left: 40px}.select2-results-dept-3 .select2-result-label{padding-left: 60px}.select2-results-dept-4 .select2-result-label{padding-left: 80px}.select2-results-dept-5 .select2-result-label{padding-left: 100px}.select2-results-dept-6 .select2-result-label{padding-left: 110px}.select2-results-dept-7 .select2-result-label{padding-left: 120px}.select2-results .select2-highlighted{background: #3875d7; color: #fff;}.select2-results li em{background: #feffde; font-style: normal;}.select2-results .select2-highlighted em{background: transparent;}.select2-results .select2-highlighted ul{background: #fff; color: #000;}.select2-results .select2-no-results, .select2-results .select2-searching, .select2-results .select2-ajax-error, .select2-results .select2-selection-limit{background: #f4f4f4; display: list-item; padding-left: 5px;}.select2-results .select2-disabled.select2-highlighted{color: #666; background: #f4f4f4; display: list-item; cursor: default;}.select2-results .select2-disabled{background: #f4f4f4; display: list-item; cursor: default;}.select2-results .select2-selected{display: none;}.select2-more-results.select2-active{background: #f4f4f4 url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%;}.select2-results .select2-ajax-error{background: rgba(255, 50, 50, .2);}.select2-more-results{background: #f4f4f4; display: list-item;}.select2-container.select2-container-disabled .select2-choice{background-color: #f4f4f4; background-image: none; border: 1px solid #ddd; cursor: default;}.select2-container.select2-container-disabled .select2-choice .select2-arrow{background-color: #f4f4f4; background-image: none; border-left: 0;}.select2-container.select2-container-disabled .select2-choice abbr{display: none;}.select2-container-multi .select2-choices{height: auto !important; height: 1%; margin: 0; padding: 0 5px 0 0; position: relative; border: 1px solid #aaa; cursor: text; overflow: hidden; background-color: #fff; background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(1%, #eee), color-stop(15%, #fff)); background-image: -webkit-linear-gradient(top, #eee 1%, #fff 15%); background-image: -moz-linear-gradient(top, #eee 1%, #fff 15%); background-image: linear-gradient(to bottom, #eee 1%, #fff 15%);}html[dir="rtl"] .select2-container-multi .select2-choices{padding: 0 0 0 5px;}.select2-locked{padding: 3px 5px 3px 5px !important;}.select2-container-multi .select2-choices{min-height: 26px;}.select2-container-multi.select2-container-active .select2-choices{border: 1px solid #5897fb; outline: none; -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3); box-shadow: 0 0 5px rgba(0, 0, 0, .3);}.select2-container-multi .select2-choices li{float: left; list-style: none;}html[dir="rtl"] .select2-container-multi .select2-choices li{float: right;}.select2-container-multi .select2-choices .select2-search-field{margin: 0; padding: 0; white-space: nowrap;}.select2-container-multi .select2-choices .select2-search-field input{padding: 5px; margin: 1px 0; font-family: sans-serif; font-size: 100%; color: #666; outline: 0; border: 0; -webkit-box-shadow: none; box-shadow: none; background: transparent !important;}.select2-container-multi .select2-choices .select2-search-field input.select2-active{background: #fff url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100% !important;}.select2-default{color: #999 !important;}.select2-container-multi .select2-choices .select2-search-choice{padding: 3px 5px 3px 18px; margin: 3px 0 3px 5px; position: relative; line-height: 13px; color: #333; cursor: default; border: 1px solid #aaaaaa; border-radius: 3px; -webkit-box-shadow: 0 0 2px #fff inset, 0 1px 0 rgba(0, 0, 0, 0.05); box-shadow: 0 0 2px #fff inset, 0 1px 0 rgba(0, 0, 0, 0.05); background-clip: padding-box; -webkit-touch-callout: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; background-color: #e4e4e4; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#f4f4f4', GradientType=0); background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(20%, #f4f4f4), color-stop(50%, #f0f0f0), color-stop(52%, #e8e8e8), color-stop(100%, #eee)); background-image: -webkit-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%); background-image: -moz-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%); background-image: linear-gradient(to bottom, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%);}html[dir="rtl"] .select2-container-multi .select2-choices .select2-search-choice{margin: 3px 5px 3px 0; padding: 3px 18px 3px 5px;}.select2-container-multi .select2-choices .select2-search-choice .select2-chosen{cursor: default;}.select2-container-multi .select2-choices .select2-search-choice-focus{background: #d4d4d4;}.select2-search-choice-close{display: block; width: 12px; height: 13px; position: absolute; right: 3px; top: 4px; font-size: 1px; outline: none; background: url('/web/static/lib/select2/select2.png') right top no-repeat;}html[dir="rtl"] .select2-search-choice-close{right: auto; left: 3px;}.select2-container-multi .select2-search-choice-close{left: 3px;}html[dir="rtl"] .select2-container-multi .select2-search-choice-close{left: auto; right: 2px;}.select2-container-multi .select2-choices .select2-search-choice .select2-search-choice-close:hover{background-position: right -11px;}.select2-container-multi .select2-choices .select2-search-choice-focus .select2-search-choice-close{background-position: right -11px;}.select2-container-multi.select2-container-disabled .select2-choices{background-color: #f4f4f4; background-image: none; border: 1px solid #ddd; cursor: default;}.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice{padding: 3px 5px 3px 5px; border: 1px solid #ddd; background-image: none; background-color: #f4f4f4;}.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice .select2-search-choice-close{display: none; background: none;}.select2-result-selectable .select2-match, .select2-result-unselectable .select2-match{text-decoration: underline;}.select2-offscreen, .select2-offscreen:focus{clip: rect(0 0 0 0) !important; width: 1px !important; height: 1px !important; border: 0 !important; margin: 0 !important; padding: 0 !important; overflow: hidden !important; position: absolute !important; outline: 0 !important; left: 0px !important; top: 0px !important;}.select2-display-none{display: none;}.select2-measure-scrollbar{position: absolute; top: -10000px; left: -10000px; width: 100px; height: 100px; overflow: scroll;}@media only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min-resolution: 2dppx){.select2-search input, .select2-search-choice-close, .select2-container .select2-choice abbr, .select2-container .select2-choice .select2-arrow b{background-image: url('/web/static/lib/select2/select2x2.png') !important; background-repeat: no-repeat !important; background-size: 60px 40px !important;}.select2-search input{background-position: 100% -21px !important;}}

/* /web/static/lib/select2-bootstrap-css/select2-bootstrap.css */
 .form-control .select2-choice{border: 0; border-radius: 2px;}.form-control .select2-choice .select2-arrow{border-radius: 0 2px 2px 0;}.form-control.select2-container{height: auto !important; padding: 0;}.form-control.select2-container.select2-dropdown-open{border-color: #5897FB; border-radius: 3px 3px 0 0;}.form-control .select2-container.select2-dropdown-open .select2-choices{border-radius: 3px 3px 0 0;}.form-control.select2-container .select2-choices{border: 0 !important; border-radius: 3px;}.control-group.warning .select2-container .select2-choice, .control-group.warning .select2-container .select2-choices, .control-group.warning .select2-container-active .select2-choice, .control-group.warning .select2-container-active .select2-choices, .control-group.warning .select2-dropdown-open.select2-drop-above .select2-choice, .control-group.warning .select2-dropdown-open.select2-drop-above .select2-choices, .control-group.warning .select2-container-multi.select2-container-active .select2-choices{border: 1px solid #C09853 !important;}.control-group.warning .select2-container .select2-choice div{border-left: 1px solid #C09853 !important; background: #FCF8E3 !important;}.control-group.error .select2-container .select2-choice, .control-group.error .select2-container .select2-choices, .control-group.error .select2-container-active .select2-choice, .control-group.error .select2-container-active .select2-choices, .control-group.error .select2-dropdown-open.select2-drop-above .select2-choice, .control-group.error .select2-dropdown-open.select2-drop-above .select2-choices, .control-group.error .select2-container-multi.select2-container-active .select2-choices{border: 1px solid #B94A48 !important;}.control-group.error .select2-container .select2-choice div{border-left: 1px solid #B94A48 !important; background: #F2DEDE !important;}.control-group.info .select2-container .select2-choice, .control-group.info .select2-container .select2-choices, .control-group.info .select2-container-active .select2-choice, .control-group.info .select2-container-active .select2-choices, .control-group.info .select2-dropdown-open.select2-drop-above .select2-choice, .control-group.info .select2-dropdown-open.select2-drop-above .select2-choices, .control-group.info .select2-container-multi.select2-container-active .select2-choices{border: 1px solid #3A87AD !important;}.control-group.info .select2-container .select2-choice div{border-left: 1px solid #3A87AD !important; background: #D9EDF7 !important;}.control-group.success .select2-container .select2-choice, .control-group.success .select2-container .select2-choices, .control-group.success .select2-container-active .select2-choice, .control-group.success .select2-container-active .select2-choices, .control-group.success .select2-dropdown-open.select2-drop-above .select2-choice, .control-group.success .select2-dropdown-open.select2-drop-above .select2-choices, .control-group.success .select2-container-multi.select2-container-active .select2-choices{border: 1px solid #468847 !important;}.control-group.success .select2-container .select2-choice div{border-left: 1px solid #468847 !important; background: #DFF0D8 !important;}

/* /web/static/src/webclient/navbar/navbar.scss */
 .o_main_navbar{--o-navbar-height: 46px; --Dropdown_menu-margin-y: 0; display: -webkit-box; display: -webkit-flex; display: flex; height: var(--o-navbar-height); min-width: min-content; padding-top: 0px; padding-bottom: 0px; border-bottom: 1px solid #5a4f7f; background: #71639e; font-size: 0.875rem;}.o_main_navbar > ul{padding: 0; margin: 0; list-style: none;}.o_main_navbar .o_nav_entry, .o_main_navbar .dropdown-toggle{border-color: transparent;}.o_main_navbar .o_menu_sections .o_nav_entry, .o_main_navbar .o_menu_sections .dropdown-toggle{background: var(--NavBar-entry-backgroundColor, #71639e); border: 1px solid transparent;}.o_main_navbar .o_menu_sections .o_nav_entry:hover, .o_main_navbar .o_menu_sections .dropdown-toggle:hover{background: var(--NavBar-entry-backgroundColor--hover, rgba(0, 0, 0, 0.08));}.o_main_navbar .o_menu_sections .o_nav_entry:focus, .o_main_navbar .o_menu_sections .dropdown-toggle:focus{background: var(--NavBar-entry-backgroundColor--focus, rgba(0, 0, 0, 0.08));}.o_main_navbar .o_menu_sections .o_nav_entry:active, .o_main_navbar .o_menu_sections .dropdown-toggle:active{background: var(--NavBar-entry-backgroundColor--active, rgba(0, 0, 0, 0.08));}.o_main_navbar .o_menu_sections .dropdown.show > .dropdown-toggle{border-color: var(--NavBar-entry-borderColor-active, transparent); background: var(--NavBar-entry-backgroundColor--active, rgba(0, 0, 0, 0.08)); color: var(--NavBar-entry-color--active, #FFFFFF);}.o_main_navbar .dropdown-menu{border-top: 0; border-radius: 0 0 0.25rem 0.25rem;}.o_main_navbar .dropdown-menu .disabled{cursor: default;}.o_main_navbar .dropdown-header.dropdown-menu_group{margin-top: 0;}.o_main_navbar .dropdown-item + .dropdown-header:not(.o_more_dropdown_section_group){margin-top: .3em;}.o_main_navbar .o_dropdown_menu_group_entry.dropdown-item{padding-left: 30px;}.o_main_navbar .o_dropdown_menu_group_entry.dropdown-item + .dropdown-item:not(.o_dropdown_menu_group_entry){margin-top: .8em;}.o_main_navbar .o_navbar_apps_menu .dropdown-toggle{--NavBar-entry-padding-left: 16px; font-size: 1.2em;}.o_main_navbar .o_menu_brand{padding-left: 0; font-size: 1.2em; color: var(--NavBar-brand-color, rgba(255, 255, 255, 0.9));}.o_main_navbar .o_menu_brand:hover{background: none;}.o_main_navbar .o_menu_sections .o_more_dropdown_section_group{margin-top: .8em;}.o_main_navbar .o_menu_sections .o_more_dropdown_section_group:first-child{margin-top: -0.5rem; padding-top: 0.75rem;}.o_main_navbar .o_menu_systray{--NavBar-entry-padding-left: 0.315em; --NavBar-entry-padding-right: 0.315em;}.o_main_navbar .o_menu_systray .badge{margin-right: -.5em; border: 0; padding: 2px 4px; background-color: var(--o-navbar-badge-bg, #28a745); font-size: 11px; color: var(--o-navbar-badge-color, inherit); text-shadow: var(--o-navbar-badge-text-shadow, 1px 1px 0 rgba(0, 0, 0, 0.3)); transform: translate(-0.6em, -30%);}body.o_is_superuser .o_menu_systray{border-image: repeating-linear-gradient(135deg, #d9b904, #d9b904 10px, #373435 10px, #373435 20px) 2; border-image-width: 2px;}

/* /web/static/src/scss/animation.scss */
 @keyframes bounceIn{0%, 20%, 40%, 60%, 80%, 100%{transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);}0%{opacity: 0; transform: scale3d(0.3, 0.3, 0.3);}20%{transform: scale3d(1.1, 1.1, 1.1);}40%{transform: scale3d(0.9, 0.9, 0.9);}60%{opacity: 1; transform: scale3d(1.03, 1.03, 1.03);}80%{transform: scale3d(0.97, 0.97, 0.97);}100%{opacity: 1; transform: scale3d(1, 1, 1);}}@keyframes flash{from, 50%, to{opacity: 1;}25%, 75%{opacity: 0;}}

/* /web/static/src/core/colorpicker/colorpicker.scss */
 .o_colorpicker_widget .o_color_pick_area{height: 125px; background-image: linear-gradient(to bottom, white 0%, rgba(255, 255, 255, 0) 50%, rgba(0, 0, 0, 0) 50%, black 100%), linear-gradient(to right, gray 0%, rgba(128, 128, 128, 0) 100%); cursor: crosshair;}.o_colorpicker_widget .o_color_slider{background: linear-gradient(#F00 0%, #FF0 16.66%, #0F0 33.33%, #0FF 50%, #00F 66.66%, #F0F 83.33%, #F00 100%);}.o_colorpicker_widget .o_color_slider, .o_colorpicker_widget .o_opacity_slider{width: 4%; margin-right: 2%; cursor: pointer;}.o_colorpicker_widget .o_slider_pointer, .o_colorpicker_widget .o_opacity_pointer{position: absolute; top: auto; left: -50%; bottom: auto; right: auto; width: 200%; height: 8px; margin-top: -2px;}.o_colorpicker_widget .o_slider_pointer, .o_colorpicker_widget .o_opacity_pointer, .o_colorpicker_widget .o_picker_pointer, .o_colorpicker_widget .o_color_preview{box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.9); border: 1px solid black;}.o_colorpicker_widget .o_color_picker_inputs{font-size: 10px;}.o_colorpicker_widget .o_color_picker_inputs input{font-family: monospace !important; height: 18px; font-size: 11px;}.o_colorpicker_widget .o_color_picker_inputs .o_hex_div input{width: 7ch;}.o_colorpicker_widget .o_color_picker_inputs .o_rgba_div input{margin-right: 3px; width: 3ch;}

/* /web/static/src/scss/mimetypes.scss */
 .o_image{display: inline-block; width: 38px; height: 38px; background-image: url("/web/static/img/mimetypes/unknown.svg"); background-size: contain; background-repeat: no-repeat; background-position: center;}.o_image[data-mimetype^='image']{background-image: url("/web/static/img/mimetypes/image.svg");}.o_image[data-mimetype^='audio']{background-image: url("/web/static/img/mimetypes/audio.svg");}.o_image[data-mimetype^='text'], .o_image[data-mimetype$='rtf']{background-image: url("/web/static/img/mimetypes/text.svg");}.o_image[data-mimetype*='octet-stream'], .o_image[data-mimetype*='download'], .o_image[data-mimetype*='python']{background-image: url("/web/static/img/mimetypes/binary.svg");}.o_image[data-mimetype^='video'], .o_image[title$='.mp4'], .o_image[title$='.avi']{background-image: url("/web/static/img/mimetypes/video.svg");}.o_image[data-mimetype$='archive'], .o_image[data-mimetype$='compressed'], .o_image[data-mimetype*='zip'], .o_image[data-mimetype$='tar'], .o_image[data-mimetype*='package']{background-image: url("/web/static/img/mimetypes/archive.svg");}.o_image[data-mimetype='application/pdf']{background-image: url("/web/static/img/mimetypes/pdf.svg");}.o_image[data-mimetype^='text-master'], .o_image[data-mimetype*='document'], .o_image[data-mimetype*='msword'], .o_image[data-mimetype*='wordprocessing']{background-image: url("/web/static/img/mimetypes/document.svg");}.o_image[data-mimetype*='application/xml'], .o_image[data-mimetype$='html']{background-image: url("/web/static/img/mimetypes/web_code.svg");}.o_image[data-mimetype$='css'], .o_image[data-mimetype$='less'], .o_image[data-ext$='less']{background-image: url("/web/static/img/mimetypes/web_style.svg");}.o_image[data-mimetype*='-image'], .o_image[data-mimetype*='diskimage'], .o_image[data-ext$='dmg']{background-image: url("/web/static/img/mimetypes/disk.svg");}.o_image[data-mimetype$='csv'], .o_image[data-mimetype*='vc'], .o_image[data-mimetype*='excel'], .o_image[data-mimetype$='numbers'], .o_image[data-mimetype$='calc'], .o_image[data-mimetype*='mods'], .o_image[data-mimetype*='spreadsheet']{background-image: url("/web/static/img/mimetypes/spreadsheet.svg");}.o_image[data-mimetype^='key']{background-image: url("/web/static/img/mimetypes/certificate.svg");}.o_image[data-mimetype*='presentation'], .o_image[data-mimetype*='keynote'], .o_image[data-mimetype*='teacher'], .o_image[data-mimetype*='slideshow'], .o_image[data-mimetype*='powerpoint']{background-image: url("/web/static/img/mimetypes/presentation.svg");}.o_image[data-mimetype*='cert'], .o_image[data-mimetype*='rules'], .o_image[data-mimetype*='pkcs'], .o_image[data-mimetype$='stl'], .o_image[data-mimetype$='crl']{background-image: url("/web/static/img/mimetypes/certificate.svg");}.o_image[data-mimetype*='-font'], .o_image[data-mimetype*='font-'], .o_image[data-ext$='ttf']{background-image: url("/web/static/img/mimetypes/font.svg");}.o_image[data-mimetype*='-dvi']{background-image: url("/web/static/img/mimetypes/print.svg");}.o_image[data-mimetype*='script'], .o_image[data-mimetype*='x-sh'], .o_image[data-ext*='bat'], .o_image[data-mimetype$='bat'], .o_image[data-mimetype$='cgi'], .o_image[data-mimetype$='-c'], .o_image[data-mimetype*='java'], .o_image[data-mimetype*='ruby']{background-image: url("/web/static/img/mimetypes/script.svg");}.o_image[data-mimetype*='javascript']{background-image: url("/web/static/img/mimetypes/javascript.svg");}.o_image[data-mimetype*='calendar'], .o_image[data-mimetype$='ldif']{background-image: url("/web/static/img/mimetypes/calendar.svg");}.o_image[data-mimetype$='postscript'], .o_image[data-mimetype$='cdr'], .o_image[data-mimetype$='xara'], .o_image[data-mimetype$='cgm'], .o_image[data-mimetype$='graphics'], .o_image[data-mimetype$='draw'], .o_image[data-mimetype*='svg']{background-image: url("/web/static/img/mimetypes/vector.svg");}

/* /web/static/src/scss/ui.scss */
 :root .o_hidden{display: none !important;}.o_disabled{pointer-events: none; opacity: 0.5;}.o_text_overflow{display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top;}.dropdown-menu{max-height: 70vh; overflow: auto; background-clip: border-box;}.dropdown-toggle{white-space: nowrap;}.dropdown-toggle.o-no-caret::before, .dropdown-toggle.o-no-caret::after{content: normal;}.o_catch_attention{position: relative; z-index: 1; animation: catchAttention 200ms ease 0s infinite normal;}.o_treeEntry{padding-left: var(--treeEntry-padding-h, 1.5rem); position: relative;}.o_treeEntry:before, .o_treeEntry:after{position: absolute; left: var(--treeEntry--beforeAfter-left, calc(var(--treeEntry-padding-h, 1.5rem) * .5)); background: var(--treeEntry--beforeAfter-color, #dee2e6); content: '';}.o_treeEntry:before{top: var(--treeEntry--before-top, 0); width: 1px; height: 100%;}.o_treeEntry:after{display: var(--treeEntry--after-display, initial); top: calc(.5em + var(--treeEntry-padding-v, 0.5rem)); width: calc(var(--treeEntry-padding-h, 1.5rem) * .5); height: 1px;}.o_treeEntry:last-of-type:before{height: calc(.5em + var(--treeEntry-padding-v, 0.5rem));}@keyframes catchAttention{0%, 20%, 40%, 60%, 80%, 100%{transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);}0%{transform: translateY(-30%);}20%{transform: translateY(-25%);}40%{transform: translateY(-20%);}60%{transform: translateY(-15%);}80%{transform: translateY(-10%);}100%{transform: translateY(-5%);}}span.o_force_ltr{display: inline;}.o_force_ltr{unicode-bidi: embed; direction: ltr;}.o_object_fit_cover{object-fit: cover;}.o_object_fit_contain{object-fit: contain;}.o_image_24_cover{width: 24px; height: 24px; object-fit: cover;}.o_image_40_cover{width: 40px; height: 40px; object-fit: cover;}.o_image_64_cover{width: 64px; height: 64px; object-fit: cover;}.o_image_64_contain{width: 64px; height: 64px; object-fit: contain;}.o_image_64_max{max-width: 64px; max-height: 64px;}

/* /web/static/src/legacy/scss/ui.scss */
 .ui-autocomplete{z-index: 1056; max-width: 600px;}.ui-autocomplete .ui-menu-item > a{display: block;}.o_rtl .ui-autocomplete{direction: ltr; right: 0; left: auto;}

/* /web/static/src/legacy/scss/modal.scss */
 .modal.o_technical_modal .modal-content .modal-header .o_subtitle{margin-left: 10px;}@media (max-width: 575.98px){.modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-header .o_subtitle{color: rgba(255, 255, 255, 0.9);}}

/* /web/static/src/views/fields/translation_dialog.scss */
 .o_translation_dialog .o_language_current{font-weight: bold;}.o_translation_dialog .row{margin-bottom: 9px;}

/* /web/static/src/scss/fontawesome_overridden.scss */
 @font-face{font-family: 'FontAwesome-tiktok-only'; src: url("/web/static/src/scss/../../fonts/tiktok_only.woff"); font-weight: normal; font-style: normal; font-display: block;}@font-face{font-family: 'FontAwesome-twitter-x-only'; src: url("/web/static/src/scss/../../fonts/twitter_x_only.woff"); font-weight: normal; font-style: normal; font-display: block;}.fa.fa-tiktok{font-family: 'FontAwesome-tiktok-only' !important;}.fa.fa-tiktok:before{content: '\e07b';}.fa-twitter.fa{font-family: 'FontAwesome-twitter-x-only' !important;}.fa-twitter.fa:before{content: '\e800';}.fa-twitter-square.fa{font-family: 'FontAwesome-twitter-x-only' !important;}.fa-twitter-square.fa:before{content: '\e803';}.o_rtl .fa.fa-caret-square-o-left, .o_rtl .fa.fa-arrow-circle-o-right, .o_rtl .fa.fa-arrow-circle-o-left, .o_rtl .fa.fa-caret-square-o-right, .o_rtl .fa.fa-toggle-left, .o_rtl .fa.fa-toggle-right, .o_rtl .fa.fa-long-arrow-left, .o_rtl .fa.fa-long-arrow-right, .o_rtl .fa.fa-chevron-circle-left, .o_rtl .fa.fa-chevron-circle-right, .o_rtl .fa.fa-quote-left, .o_rtl .fa.fa-quote-right, .o_rtl .fa.fa-angle-left, .o_rtl .fa.fa-angle-right, .o_rtl .fa.fa-angle-double-left, .o_rtl .fa.fa-angle-double-right, .o_rtl .fa.fa-rotate-left, .o_rtl .fa.fa-rotate-right, .o_rtl .fa.fa-caret-left, .o_rtl .fa.fa-caret-right, .o_rtl .fa.fa-arrow-circle-left, .o_rtl .fa.fa-arrow-circle-right, .o_rtl .fa.fa-hand-o-left, .o_rtl .fa.fa-hand-o-right, .o_rtl .fa.fa-arrow-left, .o_rtl .fa.fa-arrow-right, .o_rtl .fa.fa-chevron-left, .o_rtl .fa.fa-chevron-right, .o_rtl .fa.fa-align-left, .o_rtl .fa.fa-align-right{transform: rotate(180deg);}

/* /web_editor/static/src/scss/bootstrap_overridden.scss */
 

/* /web_editor/static/src/js/editor/odoo-editor/src/style.scss */
 .odoo-editor-editable .btn{user-select: auto; cursor: text !important;}.odoo-editor-editable ::selection{background-color: rgba(117, 167, 249, 0.5) !important;}.odoo-editor-editable.o_col_resize{cursor: col-resize;}.odoo-editor-editable.o_col_resize ::selection{background-color: transparent;}.odoo-editor-editable.o_row_resize{cursor: row-resize;}.odoo-editor-editable.o_row_resize ::selection{background-color: transparent;}.o_selected_table{caret-color: transparent;}.o_selected_table ::selection{background-color: transparent !important;}.o_selected_table .o_selected_td{box-shadow: 0 0 0 100vmax rgba(117, 167, 249, 0.5) inset; border-collapse: separate;}.o_table_ui_container{position: absolute; visibility: hidden; top: 0; left: 0;}.o_table_ui{background-color: transparent; position: absolute; z-index: 10; padding: 0;}.o_table_ui:hover{visibility: visible !important;}.o_table_ui > div{position: absolute; left: 0; top: 0;}.o_table_ui .o_table_ui_menu_toggler{cursor: pointer; background-color: var(--o-table-ui-bg, #FFFFFF); color: var(--o-table-ui-color, #495057); border: 1px solid #71639e; width: 100%; height: 100%; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; align-items: center; color: #fff; background-color: rgba(113, 99, 158, 0.7);}.o_table_ui .o_table_ui_menu{display: none; cursor: pointer; background-color: var(--o-table-ui-bg, #FFFFFF); width: fit-content; border: 1px solid var(--o-table-ui-border, #dee2e6); padding: 5px 0; white-space: nowrap; margin-left: 50%;}.o_table_ui .o_table_ui_menu > div:hover{background-color: var(--o-table-ui-hover, #e9ecef);}.o_table_ui .o_table_ui_menu span{margin-right: 8px; color: var(--o-table-ui-color, #495057);}.o_table_ui .o_table_ui_menu div{padding: 2px 8px;}.o_table_ui.o_open{visibility: visible !important;}.o_table_ui.o_open .o_table_ui_menu{display: block;}.o_table_ui.o_open .o_table_ui_menu > div.o_hide{display: none;}.o_table_ui.o_row_ui{border-right: none !important; min-width: 1rem;}.o_table_ui.o_row_ui .o_table_ui_menu_toggler{min-width: 1rem;}.o_table_ui.o_row_ui .o_table_ui_menu{position: absolute; margin-left: 100%; top: 50%;}.o_table_ui.o_column_ui{border-bottom: none !important;}.odoo-editor-editable a.o_link_in_selection:not(.btn){background-color: #a6e3e2; color: black !important; border: 1px dashed #008f8c; margin: -1px;}.oe-floating{box-shadow: 0px 3px 18px rgba(0, 0, 0, 0.23); border-radius: 4px; position: absolute;}.oe-toolbar{box-sizing: border-box; position: absolute; visibility: hidden; height: fit-content; width: fit-content; padding-left: 5px; padding-right: 5px; background: #222222; color: white;}.oe-toolbar.toolbar-bottom::before{content: ''; position: absolute; width: 0; height: 0; left: var(--arrow-left-pos); top: var(--arrow-top-pos); border: transparent 10px solid; border-bottom: #222222 10px solid; z-index: 0;}.oe-toolbar:not(.toolbar-bottom)::before{content: ''; position: absolute; width: 0; height: 0; left: var(--arrow-left-pos); top: var(--arrow-top-pos); border: transparent 10px solid; border-top: #222222 10px solid; z-index: 0; pointer-events: none;}.oe-toolbar .button-group{display: inline-block; margin-right: 13px;}.oe-toolbar .button-group:last-of-type{margin-right: 0;}.oe-toolbar .btn{position: relative; box-sizing: content-box; display: inline-block; padding: 7px; color: white;}.oe-toolbar .btn:not(.disabled):hover{background: #868686;}.oe-toolbar .oe-toolbar .dropdown-menu .btn{background: #222222;}.oe-toolbar .btn.active{background: #555555;}.oe-toolbar .dropdown-toggle{background: transparent; border: none; padding: 7px;}.oe-toolbar .dropdown-toggle[aria-expanded="true"]{background: #555555;}.oe-toolbar .dropdown-menu{background: #222222; min-width: max-content; min-width: -webkit-max-content; text-align: center;}.oe-toolbar .dropdown-item{background: transparent; color: white;}.oe-toolbar .dropdown-item pre, .oe-toolbar .dropdown-item h1, .oe-toolbar .dropdown-item h2, .oe-toolbar .dropdown-item h3, .oe-toolbar .dropdown-item h4, .oe-toolbar .dropdown-item h5, .oe-toolbar .dropdown-item h6, .oe-toolbar .dropdown-item blockquote{margin: 0; color: white;}.oe-toolbar .dropdown-item:hover, .oe-toolbar .dropdown-item:focus{color: white; background: #868686;}.oe-toolbar .dropdown-item.active, .oe-toolbar .dropdown-item:active{color: white; background: #555555;}.oe-toolbar li > a.dropdown-item{color: white;}.oe-toolbar label, .oe-toolbar label span{display: inline-block;}.oe-toolbar input[type="color"]{width: 0; height: 0; padding: 0; border: none; box-sizing: border-box; position: absolute; opacity: 0; top: 100%; margin: 2px 0 0;}.oe-toolbar #colorInputButtonGroup label{margin-bottom: 0;}.oe-toolbar .color-indicator{background-color: transparent; padding-bottom: 4px;}.oe-toolbar .color-indicator.fore-color{border-bottom: 2px solid var(--fore-color); padding: 5px;}.oe-toolbar .color-indicator.hilite-color{border-bottom: 2px solid var(--hilite-color); padding: 5px;}.oe-toolbar #style .dropdown-menu{text-align: left;}.oe-tablepicker{margin: -3px 2px -6px 2px;}.oe-tablepicker-wrapper.oe-floating{padding: 3px; z-index: 1056; background-color: var(--oeTablepicker__wrapper-bg, #FFFFFF);}.oe-tablepicker-row{line-height: 0;}.oe-tablepicker{width: max-content; width: -webkit-max-content;}.oe-tablepicker .oe-tablepicker-row .oe-tablepicker-cell{display: inline-block; background-color: var(--oeTablepicker__cell-bg, #e9ecef); width: 19px; height: 19px; padding: 0; margin-inline-end: 3px; margin-bottom: 3px;}.oe-tablepicker .oe-tablepicker-row .oe-tablepicker-cell:last-of-type{margin-inline-end: 0;}.oe-tablepicker .oe-tablepicker-row .oe-tablepicker-cell.active{background-color: var(--oeTablepicker-color-accent, #71639e);}.oe-tablepicker-size{text-align: center; margin-top: 7px;}@media (max-width: 767.98px){.oe-toolbar{position: relative; overflow-x: auto; visibility: visible; width: auto; height: auto; border-bottom: 1px solid #dee2e6; border-radius: 0; background-color: white; box-shadow: none;}.oe-toolbar .btn{color: black; padding: 3px 4px !important;}.oe-toolbar .dropdown-menu{position: fixed !important;}}@media (min-width: 768px){.oe-toolbar.oe-floating{box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);}}.oe-powerbox-wrapper{z-index: 1055; background: var(--oePowerbox__wrapper-bg, #FFFFFF); color: #495057; max-height: 40vh; box-sizing: border-box; box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); min-width: max-content;}.oe-powerbox-wrapper ::-webkit-scrollbar{background: transparent;}.oe-powerbox-wrapper ::-webkit-scrollbar{width: 10px; height: 10px;}.oe-powerbox-wrapper ::-webkit-scrollbar-thumb{background: var(--oePowerbox__ScrollbarThumb-background-color, #D3D1CB);}.oe-powerbox-wrapper ::-webkit-scrollbar-track{background: var(--oePowerbox__ScrollbarTrack-background-color, #EDECE9);}.oe-powerbox-category, .oe-powerbox-noResult{color: var(--oePowerbox__category-color, #6c757d); font-size: 11px;}.oe-powerbox-noResult{display: none;}.oe-powerbox-commandWrapper.active{background: var(--oePowerbox__commandName-bg, #f8f9fa);}i.oe-powerbox-commandImg{height: 35px; width: 35px; background: var(--oePowerbox__commandImg-bg, #f8f9fa); color: var(--oePowerbox__commandImg-color, #343a40);}.oe-powerbox-commandName{font-size: 13px; color: var(--oePowerbox__commandName-color, #495057);}.oe-powerbox-commandDescription{color: var(--oePowerbox__commandDescription-color, rgba(73, 80, 87, 0.76)); font-size: 12px;}.oe-hint{position: relative;}.oe-hint:before{content: attr(placeholder); position: absolute; top: 0; left: 0; display: block; color: inherit; opacity: 0.4; pointer-events: none; text-align: inherit; width: 100%;}.oe-sidewidget-move{position: absolute; opacity: 0.6; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; align-items: center; background-color: white; color: #6e727a; border-radius: 3px; padding: 2px 3px; cursor: move; cursor: grab; right: 5px; top: 0px;}.oe-sidewidget-move:hover{opacity: 1;}.oe-dropzone-box{position: absolute;}.oe-dropzone-box-side{position: absolute;}.oe-dropzone-box-side.oe-dropzone-box-side-north{width: 100%; height: 50%; top: -1px;}.oe-dropzone-box-side.oe-dropzone-box-side-south{width: 100%; height: 50%; bottom: -1px;}.oe-dropzone-box-side.oe-dropzone-box-side-east{height: 100%; width: 3px; right: -1px;}.oe-dropzone-box-side.oe-dropzone-box-side-west{height: 100%; width: 3px; left: -1px;}.debug .oe-dropzone-box{background: rgba(255, 0, 0, 0.3);}.debug .oe-dropzone-box-side{background: #ffa600;}.debug .oe-dropzone-hook{background: rgba(255, 0, 132, 0.2);}.oe-dropzone-hook{position: absolute;}[data-oe-absolute-container-id=oe-dropzones-container]{opacity: 0.3;}[data-oe-absolute-container-id=oe-widget-hooks-container]{opacity: 0.3;}[data-oe-absolute-container-id=oe-dropzone-hint-container]{pointer-events: none;}.oe-current-drop-hint{position: absolute; background: rgba(0, 136, 255, 0.508);}.oe-editor-dragging{pointer-events: none;}.oe-absolute-container{position: absolute; isolation: isolate; height: 0; width: 0; z-index: 1;}.oe-collaboration-caret-top-square{min-height: 5px; min-width: 5px; color: #fff; text-shadow: 0 0 5px #000; position: absolute; bottom: 100%; left: -4px; white-space: nowrap;}.oe-collaboration-caret-top-square:hover{border-radius: 2px; padding: 0.3em 0.6em;}.oe-collaboration-caret-top-square:hover::before{content: attr(data-client-name);}.oe-collaboration-caret-avatar{position: absolute; height: 1.5rem; width: 1.5rem; border-radius: 50%; transition: top 0.5s, left 0.5s, opacity 0.2s;}.oe-collaboration-caret-avatar > img{position: absolute; opacity: 1; height: 100%; width: 100%; border-radius: 50%;}.oe-avatars-counters-container{pointer-events: none;}.oe-overlapping-counter{position: absolute; background-color: green; color: white; border-radius: 50%; font-size: 9px; padding: 0 4px;}code.o_inline_code{background-color: #c5c5c5; padding: 2px; margin: 2px; color: black; font-size: inherit;}

/* /web_editor/static/src/scss/wysiwyg.scss */
 :root{--o-we-toolbar-height: 40px;}.o_we_command_protector{font-weight: 400 !important;}.o_we_command_protector b, .o_we_command_protector strong{font-weight: 700 !important;}.o_we_command_protector *{font-weight: inherit !important;}.o_we_command_protector .btn{text-align: unset !important;}.wysiwyg_iframe, .note-editor{border: 1px solid #D9D9D9; margin: 0; padding: 0;}.colorpicker{--bg: #FFF; --text-rgb: 43, 43, 51; --border-rgb: var(--text-rgb); --tab-border-top: transparent; --tab-border-bottom: #D9D9D9; --btn-color-active: inset 0 0 0 2px #01bad2, inset 0 0 0 3px var(--bg), inset 0 0 0 4px rgba(var(--border-rgb), .5);}.colorpicker, .colorpicker input{color: rgba(var(--text-rgb), 1);}.colorpicker label{color: rgba(var(--text-rgb), 0.5);}.colorpicker button{outline: none;}.colorpicker .o_we_colorpicker_switch_panel{font-size: 13px; border-bottom: 1px solid var(--tab-border-bottom); box-shadow: inset 0 1px 0 var(--tab-border-top);}.colorpicker .o_we_colorpicker_switch_pane_btn, .colorpicker .o_colorpicker_reset{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;}.colorpicker .o_colorpicker_reset{margin-left: auto !important;}.colorpicker .o_colorpicker_sections{background: var(--bg);}.colorpicker .o_colorpicker_sections > *{padding-top: 8px;}.colorpicker .o_colorpicker_sections > *:first-child{padding-top: 0;}.colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_hex_div:focus-within, .colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_rgba_div:focus-within{border-color: #01bad2;}.colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_color_picker_inputs input, .colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_color_picker_inputs input:focus{border: none; outline: none;}.colorpicker .o_colorpicker_sections .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_color_pick_area, .colorpicker .o_colorpicker_sections .o_color_slider, .colorpicker .o_colorpicker_sections .o_opacity_slider:before, .colorpicker .o_colorpicker_sections .o_hex_div, .colorpicker .o_colorpicker_sections .o_rgba_div{box-shadow: inset 0 0 0 1px rgba(var(--border-rgb), 0.5);}.colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_btn{float: none; box-sizing: border-box;}.colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_color_picker_inputs we-button{border: 1px solid black; padding: 0 6px; color: white;}.colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_color_picker_inputs we-button.active{background-color: #2b2b33;}.colorpicker .o_colorpicker_sections .o_we_color_btn{position: relative; float: left; width: 12.5%; padding-top: 10%; margin: 0; border: 1px solid var(--bg);}.colorpicker .o_colorpicker_sections .o_we_color_btn.o_colorpicker_reset{background-color: transparent;}.colorpicker .o_colorpicker_sections .o_we_color_btn.o_colorpicker_reset::before{position: absolute; top: 0; left: 0; bottom: 0; right: 0; font-family: FontAwesome !important; content: "\f00d" !important; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; align-items: center; color: #e6586c;}.colorpicker .o_colorpicker_sections .o_we_color_btn.selected{box-shadow: var(--btn-color-active);}.colorpicker .o_colorpicker_sections .o_we_color_btn.o_btn_transparent::before{background-color: transparent;}.colorpicker .o_colorpicker_sections .o_colorpicker_section.o_custom_gradient_editor .o_custom_gradient_btn{border: 1px solid var(--o-we-toolbar-border);}.colorpicker .o_colorpicker_sections .o_colorpicker_section::after{content: ""; display: table; clear: both;}.colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="transparent_grayscale"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="transparent_grayscale"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="theme"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="theme"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="reset"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="reset"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="custom"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="custom"] .o_we_color_btn::after{box-shadow: inherit;}.oe-toolbar{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}.oe-toolbar .btn{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center;}.oe-toolbar .colorpicker-menu{height: auto !important; box-sizing: content-box; min-height: fit-content;}.oe-toolbar .dropdown-item.active:not(.dropdown-item_active_noarrow):before, .oe-toolbar .dropdown-item.selected:not(.dropdown-item_active_noarrow):before{transform: translate(-1.5em, 0); height: 100%; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}.oe-toolbar.oe-floating{gap: 0.35em; align-items: stretch; min-height: 40px; padding: 0 0.5em; background-color: var(--o-we-toolbar-bg, #FFF); color: var(--o-we-toolbar-color-text, #2b2b33); font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";}.oe-toolbar.oe-floating.toolbar-bottom:before{border-bottom-color: var(--o-we-toolbar-bg, #FFF);}.oe-toolbar.oe-floating:not(.toolbar-bottom):before{border-top-color: var(--o-we-toolbar-bg, #FFF);}.oe-toolbar.oe-floating.noarrow::before{display: none;}.oe-toolbar.oe-floating > .btn-group:not(.d-none) ~ .btn-group:not(.d-none):before, .oe-toolbar.oe-floating .oe-toolbar-separator:before{content: ""; width: 1px; margin-right: calc(0.35em - 1px); background: var(--o-we-toolbar-border, #D9D9D9); transform: scaleY(0.6);}.oe-toolbar.oe-floating .btn, .oe-toolbar.oe-floating .dropdown-item{padding: 3.5px 7px; color: var(--o-we-toolbar-color-clickable, #595964);}.oe-toolbar.oe-floating .btn:hover:not(.active), .oe-toolbar.oe-floating .dropdown-item:hover:not(.active){color: var(--o-we-toolbar-color-clickable-active, #000000); background-color: transparent;}.oe-toolbar.oe-floating .btn.active, .oe-toolbar.oe-floating .dropdown-item.active{background: var(--o-we-toolbar-bg-active, rgba(217, 217, 217, 0.2)); box-shadow: inset 0 0 3px RGBA(var(--o-we-toolbar-bg-active, rgba(217, 217, 217, 0.2)), 0.5);}.oe-toolbar.oe-floating .btn{border: none; border-radius: 0; background: transparent; font-weight: 400;}.oe-toolbar.oe-floating .btn.active{color: var(--o-we-toolbar-color-accent, #018597);}.oe-toolbar.oe-floating > .btn-group > .btn, .oe-toolbar.oe-floating > .btn-group > .colorpicker-group{margin: 4px auto; padding-top: 0; padding-bottom: 0;}.oe-toolbar.oe-floating .show > .btn, .oe-toolbar.oe-floating .show > .btn:hover, .oe-toolbar.oe-floating .show > .btn:focus{color: var(--o-we-toolbar-color-clickable-active, #000000);}.oe-toolbar.oe-floating .dropdown-toggle::after{content: ""; display: inline-block; width: 0; height: 0; vertical-align: middle; -moz-transform: scale(0.9999); border-bottom: 0; border-left: 0.3em solid transparent; border-right: 0.3em solid transparent; border-top: 0.3em solid var(--o-caret-color, currentColor); margin-left: .3em;}.oe-toolbar.oe-floating .dropdown-menu{margin: 0; border: 0; padding: 0; max-height: none; overflow: visible; border-top: 1px solid var(--o-we-toolbar-border, #D9D9D9); background-color: var(--o-we-toolbar-bg, #FFF); box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); border-top-left-radius: 0; border-top-right-radius: 0; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px;}.oe-toolbar.oe-floating .dropdown-menu.show{min-width: 0;}.oe-toolbar.oe-floating .dropdown-menu:not(.colorpicker-menu) > li:last-child{margin-bottom: 1em;}.oe-toolbar.oe-floating .dropdown-menu.colorpicker-menu{margin-top: 0; min-width: 222px !important;}.oe-toolbar.oe-floating .dropdown-item{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; -webkit-box-pack: start; justify-content: flex-start; padding: 0 27.2px; min-height: 34px;}.oe-toolbar.oe-floating .dropdown-item > *{color: inherit;}.oe-toolbar.oe-floating .dropdown-item.active > *, .oe-toolbar.oe-floating .dropdown-item.active:hover, .oe-toolbar.oe-floating .dropdown-item.active:focus{color: var(--o-we-toolbar-color-clickable-active, #000000);}.oe-toolbar.oe-floating .dropdown-item.active > *:before, .oe-toolbar.oe-floating .dropdown-item.active:hover:before, .oe-toolbar.oe-floating .dropdown-item.active:focus:before{top: 0; transform: translate(-17px, 0); line-height: 34px;}.oe-toolbar.oe-floating #decoration #removeFormat{display: -webkit-box; display: -webkit-flex; display: flex;}.oe-toolbar.oe-floating #colorInputButtonGroup label:last-of-type .btn{margin: 0 1px 0 -1px;}.oe-toolbar.oe-floating #colorInputButtonGroup .note-back-color-preview.dropup .dropdown-menu{left: -52px;}.oe-toolbar.oe-floating .colorpicker-group .dropdown-toggle::after{display: none;}.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button{margin-bottom: -1px; border: 0; padding: 0.25rem 0.5rem; background: transparent; color: var(--o-we-toolbar-color-clickable, #595964);}.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:hover{color: var(--o-we-toolbar-color-clickable-active, #000000);}.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active{box-shadow: inset 0 -1px 0 var(--o-we-toolbar-color-accent, #018597); color: var(--o-we-toolbar-color-clickable-active, #000000);}.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset{background: #71639e;}.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset:hover{color: #fff;}.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset:hover{background: #66598f;}.oe-toolbar.oe-floating .colorpicker{background: var(--o-we-toolbar-bg, #FFF); box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);}.oe-toolbar.oe-floating .o_image_alt{display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; max-width: 150px;}.oe-toolbar.oe-floating input#fontSizeCurrentValue{width: 20px; border: none;}#oe_snippets > .o_we_customize_panel .oe-toolbar{display: grid; align-items: stretch;}.oe-tablepicker-wrapper .oe-tablepicker .oe-tablepicker-cell{border-radius: 0;}.oe-tablepicker-wrapper .oe-tablepicker .oe-tablepicker-cell.active{background: var(--o-we-toolbar-color-accent, #018597);}body:not(.editor_has_snippets) > .oe-toolbar{z-index: 1056;}@media (max-width: 767.98px){.oe-toolbar{background-color: white;}.oe-toolbar .btn{color: black;}.oe-toolbar::before{display: none;}.oe-toolbar::after{display: none;}}.oe_edited_link{position: relative;}.oe_edited_link:not(.nav-link){display: inline-block;}.oe_edited_link::before{content: ''; border: dashed 3px #01bad2; position: absolute; inset: -5px; pointer-events: none;}.oe_edited_link:empty::after{content: "\00a0\00a0";}@keyframes fadeInDownSmall{0%{opacity: 0; transform: translate(0, -5px);}100%{opacity: 1; transform: translate(0, 0);}}@keyframes inputHighlighter{from{background: #71639e;}to{width: 0; background: transparent;}}.o_we_horizontal_collapse{width: 0 !important; padding: 0 !important; border: none !important;}.o_we_transition_ease{transition: all ease 0.35s;}body .modal .o_link_dialog input.link-style:checked + span::after{content: "\f00c"; display: inline-block; font-family: FontAwesome; margin-left: 2px;}body .modal .o_link_dialog .o_link_dialog_preview{border-left: var(--o-link-dialog-preview-border, 1px solid #dee2e6);}.o_we_progressbar:last-child hr{display: none;}.fa.o_we_selected_image::before, img.o_we_selected_image{outline: 3px solid rgba(150, 150, 220, 0.3);}.o_we_media_author{font-size: 11px; position: absolute; top: auto; left: 0; bottom: 0; right: 0; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; text-align: center; background-color: rgba(255, 255, 255, 0.7);}@media (max-width: 991.98px){#web_editor-top-edit{position: initial !important; height: initial !important; top: initial !important; left: initial !important;}.oe-toolbar.oe-floating{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; margin-bottom: 1rem; overflow-y: visible;}.oe-toolbar.oe-floating .dropdown-menu{max-height: 200px; overflow: auto;}.oe-toolbar.oe-floating .dropdown-menu.colorpicker-menu{bottom: auto;}}.note-editable .modal:not(.o_technical_modal){top: 40px; right: 0; bottom: 0; right: 288px; width: auto; height: auto;}.note-editable .modal:not(.o_technical_modal) .modal-dialog{padding: 0.5rem 0;}.o_wysiwyg_wrapper{position: relative; margin-bottom: 11px;}.o_wysiwyg_resizer{background: #f5f5f5; height: 10px; width: 100%; border-left: 1px solid #D9D9D9; border-bottom: 1px solid #D9D9D9; border-right: 1px solid #D9D9D9; cursor: row-resize; padding-top: 1px;}.o_wysiwyg_resizer_hook{width: 20px; margin: 1px auto; border-top: 1px solid #a9a9a9;}.note-editable{border: 1px solid #D9D9D9; overflow: auto; height: 100%; padding: 4px 40px 4px 4px; min-height: 10px; border-radius: 3px;}.oe-bordered-editor .note-editable{border-width: 1px; padding: 4px 40px 4px 4px; min-height: 180px;}.o_we_no_pointer_events{pointer-events: none;}.o_we_crop_widget{background-color: rgba(128, 128, 128, 0.5); position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: 1056; overflow: auto;}.o_we_crop_widget .o_we_cropper_wrapper{position: absolute;}.o_we_crop_widget .o_we_crop_buttons{margin-top: 0.5rem; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; bottom: 1rem;}.o_we_crop_widget .o_we_crop_buttons input[type=radio]{display: none;}.o_we_crop_widget .o_we_crop_buttons .btn-group{border-radius: 0.25rem; margin: 0.1rem;}.o_we_crop_widget .o_we_crop_buttons button, .o_we_crop_widget .o_we_crop_buttons label{cursor: pointer !important; padding: 0.2rem 0.3rem;}.o_we_crop_widget .o_we_crop_buttons label{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}.o_we_crop_widget .o_we_crop_buttons label.active{background-color: #000000;}.o_we_crop_widget .o_we_crop_buttons button:not(.btn), .o_we_crop_widget .o_we_crop_buttons label{margin: 0; border: none; border-right: 1px solid #2b2b33; background-color: #2b2b33; color: #D9D9D9;}.o_we_crop_widget .o_we_crop_buttons button:not(.btn):first-child, .o_we_crop_widget .o_we_crop_buttons label:first-child{border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem;}.o_we_crop_widget .o_we_crop_buttons button:not(.btn):last-child, .o_we_crop_widget .o_we_crop_buttons label:last-child{border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem; border-right: none;}[data-oe-xpath], [data-oe-xpath] [contenteditable=true]{outline: none;}.o_transform_removal{transform: none !important;}.o_edit_menu_popover{max-width: 331.2px; width: 331.2px; user-select: none; box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); border-color: rgba(0, 0, 0, 0.025); font-size: 12px; font-weight: 400 !important;}.o_edit_menu_popover .popover-arrow::before{border-right-color: rgba(0, 0, 0, 0.05);}.o_edit_menu_popover .fw-bold{font-weight: 500 !important;}.o_edit_menu_popover .o_we_preview_favicon > img{max-height: 16px; max-width: 16px;}.o_edit_menu_popover .o_we_url_link{width: 100px;}.o_edit_menu_popover .o_we_full_url{word-break: break-all; overflow: hidden; text-overflow: ellipsis; -webkit-box-orient: vertical; -webkit-line-clamp: 2;}.o_edit_menu_popover .o_we_full_url.o_we_webkit_box{display: -webkit-box;}.o_edit_menu_popover .o_we_full_url:hover{-webkit-line-clamp: unset;}textarea.o_codeview{min-height: 400px;}@keyframes fade{0%, 100%{opacity: 0;}30%, 70%{opacity: 1;}}.o-chatgpt-content{position: absolute; background: rgba(1, 186, 210, 0.5); opacity: 0; animation: fade 1.5s ease-in-out; z-index: 1; outline: 2px dashed #01bad2; outline-offset: -2px;}.o-prompt-input{position: relative;}.o-prompt-input > textarea{padding-top: 10px; padding-bottom: 10px; min-height: 40px; max-height: 110px; resize: none;}button.o-message-insert{line-height: 1;}.o-chatgpt-message > div > *:last-child, .o-chatgpt-alternative > *:last-child{margin-bottom: 0;}.o-message-error{color: #d44c59; font-weight: bold; --bg-opacity: 0.25;}

/* /web_editor/static/src/scss/wysiwyg_iframe.scss */
 iframe.wysiwyg_iframe.o_fullscreen{left: 0 !important; right: 0 !important; top: 0 !important; bottom: 0 !important; width: 100% !important; min-height: 100% !important; z-index: 1001 !important; border: 0;}.o_wysiwyg_no_transform{transform: none !important;}body.o_in_iframe{background-color: white;}body.o_in_iframe .o_editable{position: relative;}body.o_in_iframe .note-editable{border: none; padding: 0; border-radius: 0;}body.o_in_iframe #oe_snippets{top: 0;}body.o_in_iframe .iframe-editor-wrapper{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; overflow: auto;}body.o_in_iframe.oe_dropzone_active .note-editable{overflow: hidden;}body.o_in_iframe .iframe-utils-zone{display: -webkit-box; display: -webkit-flex; display: flex;}body.o_in_iframe .note-statusbar{display: none;}body.o_in_iframe #oe_snippets .email_designer_top_actions{display: -webkit-box; display: -webkit-flex; display: flex; margin: auto 9px auto auto;}body.o_in_iframe #oe_snippets .email_designer_top_actions .btn{align-items: center; width: 24px; height: 24px; background-color: #337ab7; border: 1px solid #2e6da4; border-radius: 4px; padding: 0; margin-left: 5px;}body.o_in_iframe #oe_snippets .email_designer_top_actions .o_fullscreen_btn img{margin: auto;}body.o_in_iframe textarea.o_codeview{position: absolute; font-family: 'Courier New', Courier, monospace; outline: none; resize: none; top: 0; bottom: 0; left: 0; right: 288px; width: calc(100% - 288px); height: 100%; border: none;}body.o_in_iframe .o_height_400, body.o_in_iframe .o_height_400 div.container, body.o_in_iframe .o_height_400 div.row{min-height: 400px;}body.o_in_iframe .o_height_800, body.o_in_iframe .o_height_800 div.container, body.o_in_iframe .o_height_800 div.row{min-height: 800px;}body.o_in_iframe .btn{user-select: auto;}

/* /web_editor/static/src/scss/wysiwyg_snippets.scss */
 @media (max-width: 991.98px){body.editor_enable.editor_has_snippets #web_editor-top-edit{position: initial !important; height: initial !important; top: initial !important; left: initial !important;}body.editor_enable.editor_has_snippets #web_editor-top-edit .note-popover .popover{right: 0 !important;}}.oe_snippet{position: relative; z-index: 1041; width: 77px; background-color: #3e3e46;}.oe_snippet.o_draggable_dragging{transform: rotate(-3deg) scale(1.2); box-shadow: 0 5px 25px -10px black; transition: transform 0.3s, box-shadow 0.3s;}.oe_snippet > .oe_snippet_body{display: none !important;}.oe_snippet .oe_snippet_thumbnail{width: 100%;}.oe_snippet .oe_snippet_thumbnail .oe_snippet_thumbnail_img{width: 100%; padding-top: 75%; background-repeat: no-repeat; background-size: contain; background-position: top center; overflow: hidden;}.oe_snippet .oe_snippet_thumbnail_title{display: none;}.oe_snippet:not(:hover):not(.o_disabled):not(.o_snippet_install){background-color: rgba(62, 62, 70, 0.9);}.oe_snippet:not(:hover):not(.o_disabled):not(.o_snippet_install) .oe_snippet_thumbnail{filter: saturate(0.7); opacity: .9;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button, #oe_snippets .colorpicker .o_colorpicker_reset, #oe_snippets > .o_we_customize_panel #oe-table-options .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn, #oe_snippets > .o_we_customize_panel we-button, #oe_snippets > .o_we_customize_panel we-toggler, #oe_snippets > .o_we_customize_panel .o_we_fold_icon, #oe_snippets > .o_we_customize_panel we-button.o_we_link, #oe_snippets > .o_we_customize_panel #removeFormat, #oe_snippets > .o_we_customize_panel #oe-table-delete-table, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn, #oe_snippets #snippets_menu > button{outline: none; text-decoration: none; line-height: 20px; cursor: pointer;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager [disabled].o_pager_nav_angle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button[disabled], #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > [disabled], #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > [disabled]:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > [disabled], #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button[disabled], #oe_snippets .colorpicker [disabled].o_colorpicker_reset, #oe_snippets > .o_we_customize_panel #oe-table-options [disabled].btn, #oe_snippets > .o_we_customize_panel .oe-toolbar [disabled].btn, #oe_snippets > .o_we_customize_panel we-button[disabled], #oe_snippets > .o_we_customize_panel we-toggler[disabled], #oe_snippets > .o_we_customize_panel [disabled].o_we_fold_icon, #oe_snippets > .o_we_customize_panel [disabled]#removeFormat, #oe_snippets > .o_we_customize_panel [disabled]#oe-table-delete-table, #oe_snippets > #o_scroll #snippet_custom .oe_snippet [disabled].btn, #oe_snippets .colorpicker [disabled].o_we_colorpicker_switch_pane_btn, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager [disabled].o_pager_nav_btn, #oe_snippets #snippets_menu > button[disabled]{opacity: .5;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper):not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets #snippets_menu > button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]):hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]):hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]):hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]):hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]):hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]):hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]):hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]):hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]):hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]):hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]):hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]):hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]):hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]):hover, #oe_snippets #snippets_menu > button:not([disabled]):hover{color: #FFFFFF;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_text_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_success:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_text_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_success, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_success, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_text_success, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_success{color: #40ad67;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_hover_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_success:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_hover_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_success:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_success:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_hover_success:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_success:hover{color: #40ad67;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_success:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_success, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_success, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_success, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_success{color: white; background-color: #40ad67;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_success:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_success:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_success:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_success:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_success:hover{background-color: #369156;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_text_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_info:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_text_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_info, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_info, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_text_info, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_info{color: #6999a8;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_hover_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_info:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_hover_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_info:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_info:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_hover_info:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_info:hover{color: #6999a8;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_info:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_info, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_info, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_info, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_info{color: white; background-color: #6999a8;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_info:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_info:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_info:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_info:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_info:hover{background-color: #568695;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_text_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_warning:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_text_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_warning, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_warning, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_text_warning, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_warning{color: #f0ad4e;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_hover_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_warning:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_hover_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_warning:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_warning:hover{color: #f0ad4e;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_warning:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_warning, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_warning, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_warning, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_warning{color: white; background-color: #f0ad4e;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_warning:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_warning:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_warning:hover{background-color: #ed9d2b;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_text_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_danger:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_text_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_danger, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_danger, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_text_danger, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_danger{color: #e6586c;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_hover_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_danger:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_hover_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_danger:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_danger:hover{color: #e6586c;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_danger:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_danger, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_danger, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_danger, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_danger{color: white; background-color: #e6586c;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_danger:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_danger:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_danger:hover{background-color: #e1374f;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_text_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_brand_primary:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_text_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_brand_primary, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_brand_primary, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_text_brand_primary, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_brand_primary{color: #71639e;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_brand_primary:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_hover_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_brand_primary:hover{color: #71639e;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_brand_primary:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_brand_primary, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_brand_primary{color: white; background-color: #71639e;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_brand_primary:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_brand_primary:hover{background-color: #605487;}#oe_snippets > .o_we_customize_panel .o_we_fold_icon, #oe_snippets > .o_we_customize_panel we-button.o_we_link, #oe_snippets > .o_we_customize_panel #removeFormat, #oe_snippets > .o_we_customize_panel #oe-table-delete-table, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn, #oe_snippets #snippets_menu > button{color: #9d9d9d;}#oe_snippets > .o_we_customize_panel .o_we_fold_icon svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.o_we_link svg .o_graphic, #oe_snippets > .o_we_customize_panel #removeFormat svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-delete-table svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn svg .o_graphic, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn svg .o_graphic, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn svg .o_graphic, #oe_snippets #snippets_menu > button svg .o_graphic{fill: #9d9d9d;}#oe_snippets > .o_we_customize_panel .o_we_fold_icon svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.o_we_link svg .o_subdle, #oe_snippets > .o_we_customize_panel #removeFormat svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-delete-table svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn svg .o_subdle, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn svg .o_subdle, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn svg .o_subdle, #oe_snippets #snippets_menu > button svg .o_subdle{fill: rgba(157, 157, 157, 0.5);}#oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).active svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).active svg .o_graphic, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).active svg .o_graphic, #oe_snippets #snippets_menu > button:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]):hover svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]):hover svg .o_graphic, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]):hover svg .o_graphic, #oe_snippets #snippets_menu > button:not([disabled]):hover svg .o_graphic{fill: #FFFFFF;}#oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).active svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).active svg .o_subdle, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).active svg .o_subdle, #oe_snippets #snippets_menu > button:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]):hover svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]):hover svg .o_subdle, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]):hover svg .o_subdle, #oe_snippets #snippets_menu > button:not([disabled]):hover svg .o_subdle{fill: rgba(157, 157, 157, 0.75);}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button, #oe_snippets .colorpicker .o_colorpicker_reset, #oe_snippets > .o_we_customize_panel #oe-table-options .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn, #oe_snippets > .o_we_customize_panel we-button, #oe_snippets > .o_we_customize_panel we-toggler{display: block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; padding: 0 6px; border: 1px solid transparent; border-radius: 4px; background-color: #595964; color: #D9D9D9; text-align: center;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > * svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *:not(span) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button svg .o_graphic, #oe_snippets .colorpicker .o_colorpicker_reset svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .btn svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button svg .o_graphic, #oe_snippets > .o_we_customize_panel we-toggler svg .o_graphic{fill: #D9D9D9;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > * svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *:not(span) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button svg .o_subdle, #oe_snippets .colorpicker .o_colorpicker_reset svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .btn svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button svg .o_subdle, #oe_snippets > .o_we_customize_panel we-toggler svg .o_subdle{fill: rgba(217, 217, 217, 0.5);}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]):hover svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]):hover:not(span) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]):hover svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]):hover svg .o_graphic, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]):hover svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > .active:not(.o_we_no_toggle) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > .active:not(.o_we_no_toggle):not(span) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > .active:not(.o_we_no_toggle) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.active:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets .colorpicker .active.o_colorpicker_reset:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .active.btn:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar .active.btn:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.active:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel we-toggler.active:not(.o_we_no_toggle) svg .o_graphic{fill: #FFFFFF;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]):hover svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]):hover:not(span) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]):hover svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]):hover svg .o_subdle, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]):hover svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > .active:not(.o_we_no_toggle) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > .active:not(.o_we_no_toggle):not(span) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > .active:not(.o_we_no_toggle) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.active:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets .colorpicker .active.o_colorpicker_reset:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .active.btn:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar .active.btn:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.active:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel we-toggler.active:not(.o_we_no_toggle) svg .o_subdle{fill: rgba(157, 157, 157, 0.75);}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > .active:not(.o_we_no_toggle, .o_we_collapse_toggler), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > .active:not(.o_we_no_toggle, .o_we_collapse_toggler):not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > .active:not(.o_we_no_toggle, .o_we_collapse_toggler), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.active:not(.o_we_no_toggle, .o_we_collapse_toggler), #oe_snippets .colorpicker .active.o_colorpicker_reset:not(.o_we_no_toggle, .o_we_collapse_toggler), #oe_snippets > .o_we_customize_panel #oe-table-options .active.btn:not(.o_we_no_toggle, .o_we_collapse_toggler), #oe_snippets > .o_we_customize_panel .oe-toolbar .active.btn:not(.o_we_no_toggle, .o_we_collapse_toggler), #oe_snippets > .o_we_customize_panel we-button.active:not(.o_we_no_toggle, .o_we_collapse_toggler), #oe_snippets > .o_we_customize_panel we-toggler.active:not(.o_we_no_toggle, .o_we_collapse_toggler){background-color: #2b2b33;}#oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn, #oe_snippets #snippets_menu > button{display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; justify-content: center; min-width: 0; border: none; background-color: transparent; color: inherit; font-weight: normal;}#oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn > span, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn > span, #oe_snippets #snippets_menu > button > span{display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; padding: 0.6em 0.4em 0.5em;}#oe_snippets .colorpicker .active.o_we_colorpicker_switch_pane_btn > span, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .active.o_pager_nav_btn > span, #oe_snippets #snippets_menu > button.active > span{color: #FFFFFF; box-shadow: inset 0 -2px 0 #01bad2;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div, #oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button:focus-within > div, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div{border: 1px solid transparent; border-radius: 4px; background-color: #2b2b33;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div:focus-within, #oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button:focus-within > div:focus-within, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div:focus-within{border-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div input, #oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button:focus-within > div input, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div input{box-sizing: content-box; padding: 0 6px; border: none; border-radius: 0; background-color: transparent; color: inherit;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div input:focus, #oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button:focus-within > div input:focus, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div input:focus{outline: none;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div > we-button, #oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button:focus-within > div > we-button, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div > we-button{border: none;}#oe_snippets{position: absolute; top: var(--o-we-toolbar-height); left: auto; bottom: 0; right: 0; position: fixed; z-index: 1041; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: column nowrap; flex-flow: column nowrap; width: 288px; border-left: 1px solid #2b2b33; background-color: #2b2b33; color: #D9D9D9; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; font-size: 12px; font-weight: 400; transition: transform 400ms ease 0s; transform: translateX(100%);}#oe_snippets input::-webkit-outer-spin-button, #oe_snippets input::-webkit-inner-spin-button{-webkit--webkit-appearance: none; -moz-appearance: none; appearance: none; margin: 0;}#oe_snippets input[type=number]{-moz--webkit-appearance: textfield; -moz-appearance: textfield; appearance: textfield;}#oe_snippets *::selection{background: #03e1fe; color: #000000;}#oe_snippets .o_we_website_top_actions{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-pack: start; justify-content: flex-start; width: 288px; height: 46px; min-height: 46px; background-color: #141217;}#oe_snippets .o_we_website_top_actions .btn-group, #oe_snippets .o_we_website_top_actions .btn{height: 100%;}#oe_snippets .o_we_website_top_actions .btn{border: none; border-radius: 0; padding: 0.375rem 0.75rem; font-size: 13px; font-weight: 400; line-height: 1;}#oe_snippets .o_we_website_top_actions .btn:not(.fa){font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";}#oe_snippets .o_we_website_top_actions .btn.btn-primary{color: #fff; background-color: #71639e; border-color: #71639e;}#oe_snippets .o_we_website_top_actions .btn.btn-primary:hover{color: #fff; background-color: #605486; border-color: #5a4f7e;}.btn-check:focus + #oe_snippets .o_we_website_top_actions .btn.btn-primary, #oe_snippets .o_we_website_top_actions .btn.btn-primary:focus{color: #fff; background-color: #605486; border-color: #5a4f7e; box-shadow: 0 0 0 0.25rem rgba(134, 122, 173, 0.5);}.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-primary, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-primary, #oe_snippets .o_we_website_top_actions .btn.btn-primary:active, #oe_snippets .o_we_website_top_actions .btn.btn-primary.active, .show > #oe_snippets .o_we_website_top_actions .btn.btn-primary.dropdown-toggle{color: #fff; background-color: #5a4f7e; border-color: #554a77;}.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-primary:focus, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-primary:focus, #oe_snippets .o_we_website_top_actions .btn.btn-primary:active:focus, #oe_snippets .o_we_website_top_actions .btn.btn-primary.active:focus, .show > #oe_snippets .o_we_website_top_actions .btn.btn-primary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(134, 122, 173, 0.5);}#oe_snippets .o_we_website_top_actions .btn.btn-primary:disabled, #oe_snippets .o_we_website_top_actions .btn.btn-primary.disabled{color: #fff; background-color: #71639e; border-color: #71639e;}#oe_snippets .o_we_website_top_actions .btn.btn-secondary{color: #fff; background-color: #141217; border-color: #141217;}#oe_snippets .o_we_website_top_actions .btn.btn-secondary:hover{color: #fff; background-color: #110f14; border-color: #100e12;}.btn-check:focus + #oe_snippets .o_we_website_top_actions .btn.btn-secondary, #oe_snippets .o_we_website_top_actions .btn.btn-secondary:focus{color: #fff; background-color: #110f14; border-color: #100e12; box-shadow: 0 0 0 0.25rem rgba(55, 54, 58, 0.5);}.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-secondary, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-secondary, #oe_snippets .o_we_website_top_actions .btn.btn-secondary:active, #oe_snippets .o_we_website_top_actions .btn.btn-secondary.active, .show > #oe_snippets .o_we_website_top_actions .btn.btn-secondary.dropdown-toggle{color: #fff; background-color: #100e12; border-color: #0f0e11;}.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-secondary:focus, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-secondary:focus, #oe_snippets .o_we_website_top_actions .btn.btn-secondary:active:focus, #oe_snippets .o_we_website_top_actions .btn.btn-secondary.active:focus, .show > #oe_snippets .o_we_website_top_actions .btn.btn-secondary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(55, 54, 58, 0.5);}#oe_snippets .o_we_website_top_actions .btn.btn-secondary:disabled, #oe_snippets .o_we_website_top_actions .btn.btn-secondary.disabled{color: #fff; background-color: #141217; border-color: #141217;}#oe_snippets .o_we_website_top_actions .btn:focus, #oe_snippets .o_we_website_top_actions .btn:active, #oe_snippets .o_we_website_top_actions .btn:focus:active{outline: none; box-shadow: none !important;}#oe_snippets .o_we_website_top_actions .dropdown-menu{left: auto; right: 0;}#oe_snippets .o_we_sublevel_3 > we-title::before, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_3 > .oe-table-label::before, #oe_snippets .o_we_sublevel_2 > we-title::before, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_2 > .oe-table-label::before, #oe_snippets .o_we_sublevel_1 > we-title::before, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_1 > .oe-table-label::before{content: "└"; display: inline-block; margin-right: 0.4em;}.o_rtl #oe_snippets .o_we_sublevel_3 > we-title::before, .o_rtl #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_3 > .oe-table-label::before, .o_rtl #oe_snippets .o_we_sublevel_2 > we-title::before, .o_rtl #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_2 > .oe-table-label::before, .o_rtl #oe_snippets .o_we_sublevel_1 > we-title::before, .o_rtl #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_1 > .oe-table-label::before{transform: scaleX(-1);}#oe_snippets .o_we_sublevel_2 > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_2 > .oe-table-label{padding-left: 0.6em;}#oe_snippets .o_we_sublevel_3 > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_3 > .oe-table-label{padding-left: 1.2em;}#oe_snippets #snippets_menu{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: -webkit-box; display: -webkit-flex; display: flex; align-items: end; background-color: #141217; box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2); color: #D9D9D9; height: 3rem;}#oe_snippets .tooltip{pointer-events: none !important;}#oe_snippets .o_snippet_search_filter{position: relative; box-shadow: inset 0 -1px 0 #000000, 0 10px 10px rgba(0, 0, 0, 0.2); z-index: 2;}#oe_snippets .o_snippet_search_filter, #oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input{width: 100%;}#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input{background-color: #2b2b33; padding: 10px 2em 10px 10px; border: 0; border-bottom: 1px solid transparent; color: #FFFFFF;}#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input::placeholder{font-style: italic; color: #9d9d9d;}#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input:focus{background-color: #3e3e46; outline: none;}#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset{position: absolute; top: 10px; left: auto; bottom: 10px; right: 10px; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; align-items: center; padding: 0 6px; color: #9d9d9d; cursor: pointer;}#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset:hover, #oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset:focus, #oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset.focus{color: #FFFFFF;}#oe_snippets > #o_scroll, #oe_snippets > .o_we_customize_panel{min-height: 0; overflow: auto;}#oe_snippets > #o_scroll{background-color: #191922; padding: 0 10px; height: 100%; z-index: 1;}#oe_snippets > #o_scroll .o_panel, #oe_snippets > #o_scroll .o_panel_header{padding: 10px 0;}#oe_snippets > #o_scroll .o_panel_body{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; margin-left: -2px;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%; background-clip: padding-box; border-left: 2px solid transparent; margin-bottom: 2px; user-select: none; cursor: url(/web/static/img/openhand.cur), grab;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet .oe_snippet_thumbnail_title{display: block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; white-space: normal; padding: 5px; text-align: center;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_disabled .o_snippet_undroppable{position: absolute; top: 8px; left: auto; bottom: auto; right: 6px;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install .btn.o_install_btn{position: absolute; top: 10px; left: auto; bottom: auto; right: auto; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install:not(:hover) .btn.o_install_btn{display: none;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_disabled, #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install{background-color: rgba(62, 62, 70, 0.2);}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_disabled .oe_snippet_thumbnail_img, #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install .oe_snippet_thumbnail_img{opacity: .4; filter: saturate(0) blur(1px);}#oe_snippets > #o_scroll #snippet_custom .oe_snippet{width: 100%;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn{display: -webkit-box; display: -webkit-flex; display: flex;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn{align-items: center;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail{min-width: 0;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail_title{white-space: nowrap;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail_img{flex-shrink: 0; width: 41px; height: 30px; padding: 0;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn{padding-top: 0; padding-bottom: 0; padding-left: 0;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet:not(:hover) .btn{display: none;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget{cursor: pointer;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div{display: -webkit-box; display: -webkit-flex; display: flex;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget input{cursor: text;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button{cursor: pointer; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; padding: 0 6px; line-height: 17px; text-align: center;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:hover{background-color: gray;}#oe_snippets > .o_we_customize_panel{position: relative; flex: 1;}#oe_snippets > .o_we_customize_panel .oe-toolbar{position: relative; background: transparent; margin-top: 8px; padding: 0 10px 0 15px; grid-template-areas: "typo typo style style colors" "size align list list link" "ai animate animate hilight hilight" "options options options options options" "options2 options2 options2 options2 options2" "options3 options3 options3 options3 options3"; grid-template-columns: 1fr 1fr 1fr 1fr 1fr; grid-template-rows: minmax(22px, auto) minmax(22px, auto) minmax(22px, auto) auto auto auto; row-gap: 8px; column-gap: 3px; width: 100%;}#oe_snippets > .o_we_customize_panel .oe-toolbar::before{display: none;}#oe_snippets > .o_we_customize_panel .oe-toolbar .fa{font-size: 12px;}#oe_snippets > .o_we_customize_panel .oe-toolbar .btn{display: -webkit-box; display: -webkit-flex; display: flex; padding: 2.64px 3.5px;}#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-menu{border-color: #000000; padding: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-menu > li ~ li{border-top: 1px solid transparent;}#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-item{padding-left: 2em; background-color: #595964; color: #C6C6C6; border-radius: 0; padding-top: 5px; padding-bottom: 5px;}#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-item::after{content: ""; color: #9d9d9d; right: auto; left: 0.5em;}#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-item:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-item:focus{background: #2b2b33; color: #D9D9D9;}#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-item.active{background: #42424c; color: #FFFFFF;}#oe_snippets > .o_we_customize_panel .oe-toolbar .o_we_font_size_badge{opacity: 0.6;}#oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button:focus-within{margin-top: -1px; margin-bottom: -1px;}#oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button:focus-within input{padding: 0 !important; width: calc(2ch + 12px - 2px);}#oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button.dropdown-toggle::after{display: none;}#oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button input{background-color: unset; border: none; color: unset; padding: 0; text-align: center; width: calc(2ch + 12px);}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options{grid-area: options; padding-left: 0; padding-right: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-title.o_short_title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar we-customizeblock-option .o_short_title.oe-table-label, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-title.o_short_title, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .o_short_title.oe-table-label{width: unset !important; padding-right: 0 !important;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-title.o_long_title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar we-customizeblock-option .o_long_title.oe-table-label, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-title.o_long_title, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .o_long_title.oe-table-label{width: fit-content !important; padding-right: 10px !important; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-button .o_switch, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-button .o_switch{min-width: fit-content;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .highlighted-text, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .highlighted-text{color: white; font-weight: bold; padding: 1px;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown{position: unset; width: 100%;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-toggle, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-toggle{padding: 0; width: 100%;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-toggle::after, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-toggle::after{display: none;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show{position: absolute !important; padding: 0; width: 100%; border-color: #000000;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show[data-popper-placement$="start"], #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show[data-popper-placement$="start"]{left: -10px !important;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show we-button:not(.fa), #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show we-button:not(.fa){display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; height: 34px; text-align: left; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; font-size: 12px; font-weight: 400;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show we-button:not(.fa) div, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show we-button:not(.fa) div{width: 100%;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show .dropdown-item::before, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show .dropdown-item::before{display: none;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option input::placeholder, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options input::placeholder{font-style: italic;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-input:not(.o_we_small_input), #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-input:not(.o_we_small_input), #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-input:not(.o_we_small_input) div, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-input:not(.o_we_small_input) div, #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-input:not(.o_we_small_input) input, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-input:not(.o_we_small_input) input{width: 100% !important;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option ~ we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options ~ we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option ~ #oe-table-options, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options ~ #oe-table-options{grid-area: options2;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option ~ we-customizeblock-option ~ we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options ~ we-customizeblock-option ~ we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option ~ #oe-table-options ~ we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options ~ #oe-table-options ~ we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option ~ we-customizeblock-option ~ #oe-table-options, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options ~ we-customizeblock-option ~ #oe-table-options, #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option ~ #oe-table-options ~ #oe-table-options, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options ~ #oe-table-options ~ #oe-table-options{grid-area: options3;}#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup{position: static; grid-area: colors;}#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup .dropdown-toggle:after{display: none;}#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup .colorpicker-group{display: -webkit-box; display: -webkit-flex; display: flex; align-items: stretch; position: static;}#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup #oe-text-color{border-right: 0; border-top-right-radius: 0; border-bottom-right-radius: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup #oe-fore-color{border-top-left-radius: 0; border-bottom-left-radius: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar .btn + .btn{border-top-left-radius: 0; border-bottom-left-radius: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar .btn-group > .btn:not(:last-of-type){border-top-right-radius: 0; border-bottom-right-radius: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar #list{grid-area: list;}#oe_snippets > .o_we_customize_panel .oe-toolbar #link{grid-area: link;}#oe_snippets > .o_we_customize_panel .oe-toolbar #link #unlink{display: none;}#oe_snippets > .o_we_customize_panel .oe-toolbar #font-size{grid-area: size;}#oe_snippets > .o_we_customize_panel .oe-toolbar #decoration{grid-area: style;}#oe_snippets > .o_we_customize_panel .oe-toolbar #style{grid-area: typo;}#oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle{justify-content: space-between;}#oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle span{overflow: hidden; text-overflow: ellipsis; color: white;}#oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle span pre, #oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle span blockquote{padding: 0; border: 0; color: inherit;}#oe_snippets > .o_we_customize_panel .oe-toolbar #justify{grid-area: align;}#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu{padding: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu .btn{padding: 6.6px 11px; border-width: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu .btn:hover{z-index: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu .btn + .btn{border-left-width: 1px;}#oe_snippets > .o_we_customize_panel .oe-toolbar #chatgpt{grid-area: ai;}#oe_snippets > .o_we_customize_panel .oe-toolbar #animate{grid-area: animate;}#oe_snippets > .o_we_customize_panel .oe-toolbar #highlight{grid-area: hilight;}#oe_snippets > .o_we_customize_panel .oe-toolbar #chatgpt .fa, #oe_snippets > .o_we_customize_panel .oe-toolbar #animate .fa, #oe_snippets > .o_we_customize_panel .oe-toolbar #highlight .fa{margin-right: 2px;}#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-menu.colorpicker-menu{min-width: 0; max-height: none; left: 15px; right: 10px; border: 1px solid #000000; border-radius: 4px; padding: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar :not(.dropup) > .dropdown-menu.colorpicker-menu{top: 2em;}#oe_snippets > .o_we_customize_panel .link-custom-color-border we-input, #oe_snippets > .o_we_customize_panel .link-custom-color-border we-select{max-width: max-content;}#oe_snippets > .o_we_customize_panel .link-custom-color-border we-toggler{width: 85px !important;}#oe_snippets > .o_we_customize_panel we-button.o_we_link{margin-top: 0; border: 0; padding: 0; background: 0;}#oe_snippets > .o_we_customize_panel we-toggler{padding-right: 2em; text-align: left;}#oe_snippets > .o_we_customize_panel we-toggler::after{content: ""; color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-toggler.o_we_toggler_pager{padding-right: 2em;}#oe_snippets > .o_we_customize_panel we-toggler.o_we_toggler_pager::after{content: ""; color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-toggler > img, #oe_snippets > .o_we_customize_panel we-toggler > svg{max-width: 100%;}#oe_snippets > .o_we_customize_panel we-toggler + *{display: none; border: 1px solid #000000; border-radius: 4px; background-color: #141217; box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.5);}#oe_snippets > .o_we_customize_panel we-toggler.active{padding-right: 2em;}#oe_snippets > .o_we_customize_panel we-toggler.active::after{content: ""; color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-toggler.active + *{display: block;}#oe_snippets > .o_we_customize_panel we-toggler.active + .o_we_has_pager{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button, #oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-item, #oe_snippets > .o_we_customize_panel we-toggler, #oe_snippets > .o_we_customize_panel we-toggler.o_we_toggler_pager, #oe_snippets > .o_we_customize_panel we-toggler.active{position: relative;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active::after, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button::after, #oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-item::after, #oe_snippets > .o_we_customize_panel we-toggler::after, #oe_snippets > .o_we_customize_panel we-toggler.o_we_toggler_pager::after, #oe_snippets > .o_we_customize_panel we-toggler.active::after{position: absolute; top: 50%; left: auto; bottom: auto; right: 0.5em; transform: translateY(-50%); width: 1em; text-align: center; font-family: FontAwesome;}#oe_snippets > .o_we_customize_panel we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-label{display: block; text-align: left;}#oe_snippets > .o_we_customize_panel we-title:where(:lang(en)), #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-label:where(:lang(en)){text-transform: capitalize;}#oe_snippets > .o_we_customize_panel we-customizeblock-options{position: relative; display: block; padding: 0 0 15px 0; background-color: #3e3e46;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: 3px 10px 0 15px; background-color: #2b2b33; font-size: 13px;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > span, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > span{display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; cursor: pointer; color: #FFFFFF !important; line-height: 32px;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: -webkit-box; display: -webkit-flex; display: flex; margin-left: auto; font-size: .9em;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group .oe_snippet_remove, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group .oe_snippet_remove{font-size: 1.2em;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-customizeblock-option, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-customizeblock-option, #oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group #oe-table-options, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group #oe-table-options{display: -webkit-box; display: -webkit-flex; display: flex; padding: 0;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-button, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-button{margin-top: 0 !important; margin-left: 3px; padding: 0 3px !important;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-button.fa, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-button.fa, #oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-button.o_we_icon_button, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-button.o_we_icon_button{box-sizing: content-box; width: 1.29em; padding: 0 0.15em !important; margin-left: 6px; text-align: center; justify-content: center;}#oe_snippets > .o_we_customize_panel we-customizeblock-options.o_we_outdated_block_options{padding: 0 !important;}#oe_snippets > .o_we_customize_panel we-customizeblock-options.o_we_outdated_block_options > we-customizeblock-option:not(.snippet-option-VersionControl), #oe_snippets > .o_we_customize_panel we-customizeblock-options.o_we_outdated_block_options > #oe-table-options:not(.snippet-option-VersionControl){display: none !important;}#oe_snippets > .o_we_customize_panel we-customizeblock-options.o_we_outdated_block_options > we-customizeblock-option.snippet-option-VersionControl, #oe_snippets > .o_we_customize_panel we-customizeblock-options.o_we_outdated_block_options > .snippet-option-VersionControl#oe-table-options{padding: 0 !important;}#oe_snippets > .o_we_customize_panel we-customizeblock-option, #oe_snippets > .o_we_customize_panel #oe-table-options{position: relative; display: block; padding: 0 10px 0 15px;}#oe_snippets > .o_we_customize_panel we-customizeblock-option .dropdown-menu, #oe_snippets > .o_we_customize_panel #oe-table-options .dropdown-menu{position: static !important;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-alert, #oe_snippets > .o_we_customize_panel #oe-table-options > we-alert{background-color: #6999a8; display: block; padding: 6px;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-alert we-title, #oe_snippets > .o_we_customize_panel #oe-table-options > we-alert we-title, #oe_snippets > .o_we_customize_panel we-customizeblock-option > we-alert #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-option > we-alert .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options > we-alert .oe-table-label{margin-bottom: 6px; text-transform: uppercase; font-weight: bold;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-option > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options > .oe-table-label{margin-bottom: -4px; font-size: 13px; color: #FFFFFF; font-weight: 500;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-title:not(:first-child), #oe_snippets > .o_we_customize_panel #oe-table-options > we-title:not(:first-child), #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-option > .oe-table-label:not(:first-child), #oe_snippets > .o_we_customize_panel #oe-table-options > .oe-table-label:not(:first-child){margin-top: 16px;}#oe_snippets > .o_we_customize_panel .o_we_fold_icon{position: absolute; top: 0; left: -15px; bottom: 0; right: 100%; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; align-items: center; width: 15px;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget{margin-top: 8px;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > div{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; min-height: 22px;}#oe_snippets > .o_we_customize_panel .o_we_large > div{flex: 1 1 auto !important; width: 100%;}#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div{display: block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; min-height: 20px;}#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div, #oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div > .fa{line-height: 20px;}#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div > img{margin-bottom: 1px;}#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div > svg{margin-bottom: 2px;}#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget.fa > div, #oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget.oi > div{display: none;}#oe_snippets > .o_we_customize_panel we-button.o_we_icon_button, #oe_snippets > .o_we_customize_panel we-button.fa{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;}#oe_snippets > .o_we_customize_panel we-button.fa-fw, #oe_snippets > .o_we_customize_panel we-button.oi-fw{padding: 0 .5em; width: 2.29em; justify-content: center;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget{min-width: 20px; padding: 0; border: none; background: none; cursor: default;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options we-button.o_we_checkbox_wrapper.o_we_user_value_widget > .oe-table-label{cursor: pointer;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget > div{display: -webkit-box; display: -webkit-flex; display: flex; min-height: 22px; line-height: 22px;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget we-checkbox{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; width: 20px; height: 12px; background-color: #9d9d9d; border-radius: 10rem; cursor: pointer;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget we-checkbox::after{content: ""; display: block; width: 11px; height: 10px; border-radius: 10rem; background-color: #FFFFFF;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget.active we-checkbox{background-color: #01bad2; -webkit-box-pack: end; justify-content: flex-end;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget.active, #oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget:hover{color: #D9D9D9;}#oe_snippets > .o_we_customize_panel we-selection-items .o_we_user_value_widget{margin-top: 0; flex-grow: 1;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget{position: relative;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_icon_select) we-toggler{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 157.8px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_select_pager).o_we_widget_opened .o_we_dropdown_caret{position: relative; display: block; align-self: flex-end;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_select_pager).o_we_widget_opened .o_we_dropdown_caret::before, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_select_pager).o_we_widget_opened .o_we_dropdown_caret::after{content: ''; position: absolute; top: 100%; left: auto; bottom: auto; right: 2em; z-index: 1001; transform: translateX(50%); margin-top: 2px; border-bottom: 7px solid #000000; border-left: 8px solid transparent; border-right: 8px solid transparent;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_select_pager).o_we_widget_opened .o_we_dropdown_caret::after{border-bottom-color: #595964; border-left-width: 7px; border-right-width: 7px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_so_color_palette) ~ we-button:not(:hover):not(.o_we_image_shape_remove):last-child{background: none;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-toggler:empty::before{content: attr(data-placeholder-text);}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:not(.o_we_has_pager){position: absolute; top: 100%; left: 0; bottom: auto; right: 0; z-index: 1000; max-height: 600px; overflow-y: auto;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager{width: 288px; max-height: calc(100% - calc(46px + 3rem)); margin-top: calc(46px + 3rem);}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager we-title, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .oe-table-label{padding: 3px 10px 0 15px; background-color: #2b2b33; line-height: 32px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav{background-color: #2b2b33;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav > div:first-child{padding-left: 12px; padding-top: 8px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_title{padding-left: 12px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle{color: #fff; background-color: #2b2b33; border-color: #2b2b33; padding: 4px; font-size: 16.8px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:hover{color: #fff; background-color: #25252b; border-color: #222229;}.btn-check:focus + #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:focus{color: #fff; background-color: #25252b; border-color: #222229; box-shadow: 0 0 0 0.25rem rgba(75, 75, 82, 0.5);}.btn-check:checked + #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle, .btn-check:active + #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:active, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle.active, .show > #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle.dropdown-toggle{color: #fff; background-color: #222229; border-color: #202026;}.btn-check:checked + #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:focus, .btn-check:active + #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:focus, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:active:focus, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle.active:focus, .show > #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(75, 75, 82, 0.5);}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:disabled, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle.disabled{color: #fff; background-color: #2b2b33; border-color: #2b2b33;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:focus{box-shadow: none;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_container{overflow-y: scroll; scroll-behavior: smooth; background-color: #3e3e46;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:not(.dropdown-menu):not(.o_we_has_pager){margin-top: 8px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:empty{line-height: 34px; background-color: #595964; color: #C6C6C6; padding-left: 2em;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:empty::before{content: attr(data-placeholder-text);}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-title, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > .oe-table-label{line-height: 34px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button{padding-left: 2em; border: none; background: none; background-clip: padding-box; background-color: #595964; color: #C6C6C6; border-radius: 0; text-align: left;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button::after{content: ""; color: #9d9d9d; right: auto; left: 0.5em;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label{flex-grow: 1;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > div, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label{line-height: 34px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > div img, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > div svg, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title img, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label img, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label img, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title svg, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label svg, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label svg{max-width: 100%;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.o_we_badge_at_end > div{display: -webkit-box; display: -webkit-flex; display: flex; width: 100%; justify-content: space-between;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button:not(.d-none) ~ we-button{border-top: 1px solid transparent;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button:hover{background-color: #2b2b33; color: #D9D9D9;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active{padding-left: 2em; background-color: #42424c; color: #FFFFFF;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active::after{content: ""; color: #9d9d9d; right: auto; left: 0.5em;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active:after{color: #01bad2;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_dropdown_up we-selection-items{top: auto !important; bottom: 100% !important; margin-bottom: 8px !important;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_dropdown_up .o_we_dropdown_caret{align-self: flex-start !important;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_dropdown_up .o_we_dropdown_caret::before, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_dropdown_up .o_we_dropdown_caret::after{top: auto !important; bottom: 100% !important; margin-bottom: 2px; transform: rotate(180deg) translateX(-50%) !important;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_dropdown_up.o_we_so_color_palette .o_we_dropdown_caret::before, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_dropdown_up.o_we_so_color_palette .o_we_dropdown_caret::after{margin-bottom: 1px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page{display: grid; grid-template-columns: repeat(4, 1fr); gap: 4px; padding: 8px; background-color: #3e3e46;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page we-button{padding: 8px; background-color: transparent;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page we-button[data-shape]{grid-column: span 4; padding: 0;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page we-button[data-shape] div{width: 100%; height: 75px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page we-button.active{outline: 4px solid #40ad67; outline-offset: -4px; background-color: rgba(64, 173, 103, 0.2);}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page img{width: 100%; aspect-ratio: 1; object-fit: contain;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-selection-items we-button{padding: 20px 5px; border: 1px solid #000000; border-radius: 2px; justify-content: center;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-selection-items we-button.active{border: 2px solid #40ad67 !important;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-selection-items we-button[data-set-text-highlight]{--text-highlight-width: .15em; --text-highlight-color: #D9D9D9;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-selection-items we-button[data-set-text-highlight] > div{flex: none; position: relative; width: 60%; font-size: 15.6px; font-weight: bold; overflow: visible; isolation: isolate;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-selection-items we-button[data-set-text-highlight] > div svg{z-index: -1;}#oe_snippets > .o_we_customize_panel we-button.o_we_image_shape_remove div{display: -webkit-box; display: -webkit-flex; display: flex;}#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 1; max-width: 100%;}#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button{padding: 0 6px; border-radius: 0;}#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button + we-button{border-left: none;}#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button:first-child, #oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button .active:first-child{border-top-left-radius: 4px; border-bottom-left-radius: 4px;}#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button:last-child, #oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button .active:last-child{border-top-right-radius: 4px; border-bottom-right-radius: 4px;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-button-group.o_we_user_value_widget we-selection-items, #oe_snippets > .o_we_customize_panel #oe-table-options > we-button-group.o_we_user_value_widget we-selection-items{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 157.8px;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-button-group.o_we_user_value_widget we-selection-items we-button, #oe_snippets > .o_we_customize_panel #oe-table-options > we-button-group.o_we_user_value_widget we-selection-items we-button{display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; flex: 1 1 25%; padding: 1.5px 2px;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div{-webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto; width: 60px;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget input{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 0; min-width: 2ch; height: 20px; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget input::placeholder{color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget input.datetimepicker-input.text-primary{color: inherit !important;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget span{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; padding-right: 6px; font-size: 11px; color: #9d9d9d;}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: block; width: 20px; height: 20px; border: 1px solid #000000; border-radius: 10rem; cursor: pointer;}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview::after{box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.5);}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened .o_we_color_preview{border: 2px solid #01bad2;}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened span.o_we_dropdown_caret::before, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened span.o_we_dropdown_caret::after{right: 10px;}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened span.o_we_dropdown_caret::after{border-bottom-width: 8px;}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget we-toggler{display: none;}#oe_snippets > .o_we_customize_panel we-matrix{overflow-y: auto;}#oe_snippets > .o_we_customize_panel we-matrix table{table-layout: fixed; width: 100%;}#oe_snippets > .o_we_customize_panel we-matrix table td, #oe_snippets > .o_we_customize_panel we-matrix table th{text-align: center;}#oe_snippets > .o_we_customize_panel we-matrix table td we-button, #oe_snippets > .o_we_customize_panel we-matrix table th we-button{display: inline-block; color: inherit; height: 100%;}#oe_snippets > .o_we_customize_panel we-matrix table td we-button.o_we_matrix_remove_col, #oe_snippets > .o_we_customize_panel we-matrix table td we-button.o_we_matrix_remove_row, #oe_snippets > .o_we_customize_panel we-matrix table th we-button.o_we_matrix_remove_col, #oe_snippets > .o_we_customize_panel we-matrix table th we-button.o_we_matrix_remove_row{display: none;}#oe_snippets > .o_we_customize_panel we-matrix table td input, #oe_snippets > .o_we_customize_panel we-matrix table th input{border: 1px solid transparent; background-color: #2b2b33; color: inherit; font-size: 12px; width: 100%;}#oe_snippets > .o_we_customize_panel we-matrix table td:last-child, #oe_snippets > .o_we_customize_panel we-matrix table th:last-child{width: 28px;}#oe_snippets > .o_we_customize_panel we-matrix table tr:last-child we-button{overflow: visible;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget[data-display-range-value] input[type="range"]{min-width: 0;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 157.8px; height: 22px; padding: 0 1px 0 0; background-color: transparent; -webkit-appearance: none; -moz-appearance: none; appearance: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]:focus{outline: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]:focus::-webkit-slider-thumb{box-shadow: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]:focus::-moz-range-thumb{box-shadow: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]:focus::-ms-thumb{box-shadow: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-moz-focus-outer{border: 0;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-webkit-slider-thumb{width: 10px; height: 10px; margin-top: -3px; border: none; border-radius: 10rem; background-color: #01bad2; box-shadow: none; -webkit-appearance: none; -moz-appearance: none; appearance: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-webkit-slider-thumb:active{background-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-webkit-slider-runnable-track{width: 100%; height: 4px; cursor: pointer; background-color: #9d9d9d; border-color: transparent; border-radius: 10rem; box-shadow: none; position: relative;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-moz-range-thumb{width: 10px; height: 10px; border: none; border-radius: 10rem; background-color: #01bad2; box-shadow: none; -webkit-appearance: none; -moz-appearance: none; appearance: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-moz-range-thumb:active{background-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-moz-range-track{width: 100%; height: 4px; cursor: pointer; background-color: #9d9d9d; border-color: transparent; border-radius: 10rem; box-shadow: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-moz-range-progress{background-color: #01bad2; height: 4px; border-color: transparent; border-radius: 10rem;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-ms-thumb{width: 10px; height: 10px; margin-top: 0; margin-right: 0; margin-left: 0; border: none; border-radius: 10rem; background-color: #01bad2; box-shadow: none; -webkit-appearance: none; -moz-appearance: none; appearance: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-ms-thumb:active{background-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-ms-track{width: 100%; height: 4px; cursor: pointer; background-color: transparent; border-color: transparent; border-width: 5px; box-shadow: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-ms-fill-lower{background-color: #01bad2; border-radius: 10rem; border-radius: 1rem;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-ms-fill-upper{background-color: #9d9d9d; border-radius: 10rem;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"].o_we_inverted_range{transform: rotate(180deg);}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"].o_we_inverted_range::-moz-range-track{background-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"].o_we_inverted_range::-moz-range-progress{background-color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"].o_we_inverted_range::-ms-fill-lower{background-color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"].o_we_inverted_range::-ms-fill-upper{background-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-list > div{-webkit-flex-flow: row wrap; flex-flow: row wrap;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper{width: 100%; max-height: 200px; overflow-y: auto;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table{table-layout: auto; width: 100%; margin-bottom: 4px;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table:empty{margin-bottom: 0;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table input{width: 100%; border: 1px solid transparent; border-radius: 4px; padding: 0 6px; background-color: #2b2b33; color: inherit; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table tr{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; border: none;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table td{flex-grow: 1; padding-bottom: 4px;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table td:not(.o_we_list_record_name){flex-grow: 0;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table td we-button.o_we_checkbox_wrapper{margin: 0 0 0 0.3em;}#oe_snippets > .o_we_customize_panel we-list .o_we_user_value_widget{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}#oe_snippets > .o_we_customize_panel we-multi.o_we_user_value_widget{margin-top: 0;}#oe_snippets > .o_we_customize_panel we-multi.o_we_user_value_widget > div{-webkit-flex-flow: row wrap; flex-flow: row wrap;}#oe_snippets > .o_we_customize_panel we-multi.o_we_user_value_widget > div > *{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}#oe_snippets > .o_we_customize_panel .o_we_m2o_search{background-color: #595964; flex-grow: 1 !important; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; margin-bottom: 1px; border-radius: 4px; padding: .25em .5em;}#oe_snippets > .o_we_customize_panel .o_we_m2o_search::before{content: "\f002"; font-size: 1.2em; padding-right: .5em; font-family: FontAwesome;}#oe_snippets > .o_we_customize_panel .o_we_m2o_search input{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; color: inherit; border: 1px solid transparent; border-radius: 4px; background-color: #2b2b33; padding: 1px 6px;}#oe_snippets > .o_we_customize_panel .o_we_m2o_search input:focus{outline: none; border-color: #01bad2;}#oe_snippets > .o_we_customize_panel .o_we_m2o_search input::placeholder{color: #9d9d9d;}#oe_snippets > .o_we_customize_panel .o_we_m2o_search_more{color: var(--o-cc1-btn-primary); margin-top: 1px; width: 100%; cursor: pointer; padding-left: 2em; line-height: 20px;}#oe_snippets > .o_we_customize_panel .o_we_m2o_create{margin-top: 1px;}#oe_snippets > .o_we_customize_panel .o_we_m2m we-list, #oe_snippets > .o_we_customize_panel .o_we_m2m we-list > div, #oe_snippets > .o_we_customize_panel .o_we_m2m we-list we-select{margin-top: 0; max-width: 100%;}#oe_snippets > .o_we_customize_panel .o_we_m2m we-title, #oe_snippets > .o_we_customize_panel .o_we_m2m #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_m2m .oe-table-label{align-self: flex-start;}#oe_snippets > .o_we_customize_panel we-row, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row{position: relative; margin-top: 8px;}#oe_snippets > .o_we_customize_panel we-row .o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_we_user_value_widget{margin-top: 0; min-width: 4em;}#oe_snippets > .o_we_customize_panel we-row we-button.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row we-button.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel we-row .o_we_so_color_palette.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_we_so_color_palette.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel we-row we-button-group.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row we-button-group.o_we_user_value_widget{min-width: auto;}#oe_snippets > .o_we_customize_panel we-row.o_we_header_font_row > div, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_header_font_row.oe-table-row > div{justify-content: space-between;}#oe_snippets > .o_we_customize_panel we-row.o_we_header_font_row > div we-select, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_header_font_row.oe-table-row > div we-select{max-width: fit-content; min-width: fit-content; margin-right: 0px !important;}#oe_snippets > .o_we_customize_panel we-row > div, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > div{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}#oe_snippets > .o_we_customize_panel we-row > div > :not(.d-none) ~ *, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > div > :not(.d-none) ~ *{margin-left: 3px;}#oe_snippets > .o_we_customize_panel we-row we-select.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row we-select.o_we_user_value_widget{position: static;}#oe_snippets > .o_we_customize_panel we-row.o_we_full_row > div, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_full_row.oe-table-row > div{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}#oe_snippets > .o_we_customize_panel we-row.o_short_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_short_title.oe-table-row we-title, #oe_snippets > .o_we_customize_panel we-row.o_short_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row.o_short_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .o_short_title.oe-table-row .oe-table-label, #oe_snippets > .o_we_customize_panel we-row .o_short_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_short_title we-title, #oe_snippets > .o_we_customize_panel we-row .o_short_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row .o_short_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_short_title .oe-table-label{width: unset !important; padding-right: 0 !important;}#oe_snippets > .o_we_customize_panel we-row.o_long_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_long_title.oe-table-row we-title, #oe_snippets > .o_we_customize_panel we-row.o_long_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row.o_long_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .o_long_title.oe-table-row .oe-table-label, #oe_snippets > .o_we_customize_panel we-row .o_long_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_long_title we-title, #oe_snippets > .o_we_customize_panel we-row .o_long_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row .o_long_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_long_title .oe-table-label{width: fit-content !important; padding-right: 10px !important;}#oe_snippets > .o_we_customize_panel we-row.o_design_tab_title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_design_tab_title.oe-table-row{margin-top: 15px;}#oe_snippets > .o_we_customize_panel we-row.o_design_tab_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_design_tab_title.oe-table-row we-title, #oe_snippets > .o_we_customize_panel we-row.o_design_tab_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row.o_design_tab_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .o_design_tab_title.oe-table-row .oe-table-label{font-weight: 600;}#oe_snippets > .o_we_customize_panel we-row .o_we_so_color_palette.o_we_user_value_widget + .o_we_user_value_widget:not(.o_we_so_color_palette), #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_we_so_color_palette.o_we_user_value_widget + .o_we_user_value_widget:not(.o_we_so_color_palette){margin-left: 12px !important;}#oe_snippets > .o_we_customize_panel we-row:has(div > we-button-group + we-select.o_grid) we-button-group we-selection-items, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:has(div > we-button-group + we-select.o_grid) we-button-group we-selection-items{display: grid !important; grid-template-columns: auto auto;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget, #oe_snippets > .o_we_customize_panel we-row, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: row wrap; flex-flow: row wrap;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > we-title, #oe_snippets > .o_we_customize_panel we-row > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_user_value_widget > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > .oe-table-label{width: 100%;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > div, #oe_snippets > .o_we_customize_panel we-row > div, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > div{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; min-width: 0; margin-top: 8px;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > div svg, #oe_snippets > .o_we_customize_panel we-row > div svg, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > div svg{margin: 0 auto;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw), #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw), #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw){-webkit-flex-flow: row nowrap; flex-flow: row nowrap; align-items: center;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw):not(we-input):not(.o_we_so_color_palette), #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw):not(we-input):not(.o_we_so_color_palette), #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw):not(we-input):not(.o_we_so_color_palette){flex-grow: 1;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw) > we-title, #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw) > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw) > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_user_value_widget:not(.o_we_fw) > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row:not(.o_we_fw) > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw) > .oe-table-label{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; width: 105.2px; padding-right: 6px;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw) > div, #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw) > div, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw) > div{margin-top: 0;}#oe_snippets > .o_we_customize_panel we-collapse{position: relative; display: block; padding-left: 15px; padding-right: 10px; margin-right: -10px; margin-left: -15px; border-top: 4px solid transparent; padding-bottom: 4px; margin-bottom: -4px; background-clip: padding-box;}#oe_snippets > .o_we_customize_panel we-collapse > :first-child, #oe_snippets > .o_we_customize_panel we-collapse .o_we_collapse_toggler{margin-top: 4px;}#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler{position: absolute; top: 0; left: 0; bottom: auto; right: auto; width: 15px; height: 22px; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center; padding: 0; background: none; border: none;}#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler::after{content: '\f0da'; position: static; transform: none;}.o_rtl #oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler::after{transform: scaleX(-1);}#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler.active::after{content: '\f0d7';}#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler.active + *{background: none; border: none; box-shadow: none;}#oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active, #oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active we-toggler.o_we_collapse_toggler{background-color: #3e3e46;}#oe_snippets > .o_we_customize_panel we-collapse.active, #oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active we-collapse.active, #oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active we-collapse.active .o_we_collapse_toggler{background-color: #3e3e46;}#oe_snippets > .o_we_customize_panel we-collapse .o_we_collapse_toggler{cursor: pointer;}#oe_snippets > .o_we_customize_panel .o_we_image_weight{margin-left: 12px;}#oe_snippets > .o_we_customize_panel we-button + .o_we_image_weight{margin-left: 6px;}#oe_snippets > .o_we_customize_panel .o_we_tag{background-color: #000000; white-space: nowrap; padding: 1.5px 3px; border-radius: 3px; font-size: 0.85em;}#oe_snippets > .o_we_invisible_el_panel{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; max-height: 220px; overflow-y: auto; margin-top: auto; padding: 10px; background-color: #191922; box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);}#oe_snippets > .o_we_invisible_el_panel .o_panel_header{padding: 8px 0;}#oe_snippets > .o_we_invisible_el_panel .o_we_invisible_entry{padding: 8px 6px; cursor: pointer;}#oe_snippets > .o_we_invisible_el_panel .o_we_invisible_entry:hover{background-color: #2b2b33;}#oe_snippets > .o_we_invisible_el_panel div.o_we_invisible_root_parent{padding-bottom: 3px;}#oe_snippets > .o_we_invisible_el_panel ul{list-style: none; padding-inline-start: 15px; margin-bottom: 5px;}#oe_snippets > .o_we_invisible_el_panel ul div.o_we_invisible_entry{padding-top: 3px; padding-bottom: 3px;}#oe_snippets.o_we_backdrop > .o_we_customize_panel{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}#oe_snippets.o_we_backdrop > .o_we_customize_panel:not(:has(.o_we_select_pager.o_we_widget_opened))::after{content: ""; position: -webkit-sticky; position: sticky; top: auto; left: 0; bottom: 0; right: 0; display: block; height: 100vh; margin-top: -100vh; pointer-events: none; background: rgba(0, 0, 0, 0.2);}#oe_snippets.o_we_backdrop .o_we_widget_opened{z-index: 1000;}.o_we_cc_preview_wrapper{font-family: sans-serif !important; font-size: 15px !important; padding: 8px 8px 6.4px;}.o_we_cc_preview_wrapper > *{margin-bottom: 0 !important; line-height: 1 !important;}.o_we_color_combination_btn_text{color: inherit !important; font-family: inherit !important; font-size: 0.8em !important; margin-top: 0.5em !important;}.o_we_color_combination_btn_title{margin-top: 0 !important; font-size: 1.3em !important;}.o_we_color_combination_btn_btn{padding: 0.2em 3px 0.3em !important; border-radius: 2px !important; font-size: 0.8em !important;}.o_we_border_preview{display: inline-block; width: 999px; max-width: 100%; margin-bottom: 2px; border-width: 4px; border-bottom: none !important;}we-select.o_we_border_preview_aligned_select{width: 60px;}#oe_snippets .colorpicker{--bg: #3e3e46; --text-rgb: 217, 217, 217; --border-rgb: var(--text-rgb); --tab-border-top: rgba(255, 255, 255, .2); --tab-border-bottom: #191922; --btn-color-active: inset 0 0 0 1px #3e3e46, inset 0 0 0 3px #01bad2, inset 0 0 0 4px white;}#oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn{-webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto;}#oe_snippets .colorpicker .o_colorpicker_reset{border: 0; background-color: transparent;}#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn{float: none; width: 100%; padding: 0; margin: 0; border: 0; background-color: transparent; background-clip: padding-box; border-top: 8px solid transparent; border-bottom: 8px solid transparent;}#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn + .o_we_color_combination_btn{margin-top: -4px;}#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn.selected > .o_we_cc_preview_wrapper{box-shadow: 0 0 0 1px #40ad67 !important;}#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn.selected .o_we_color_combination_btn_title::before{content: "\f00c"; margin-right: 8px; font-size: 0.8em; font-family: FontAwesome; color: #40ad67;}#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn .o_we_cc_preview_wrapper:after{bottom: -1px;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor{font-size: 12px;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_btn{color: #ffffff; background-color: #3e3e46; float: none; box-sizing: border-box;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor span.o_custom_gradient_input{border: 1px solid black;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor span.o_custom_gradient_input input{outline: none;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor span.o_custom_gradient_input:focus-within{border-color: #01bad2;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale{cursor: copy;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale div{height: 20px;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi{display: grid;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]{pointer-events: none; grid-column: 1/span 2; grid-row: 3; background: none; -webkit-appearance: none; -moz-appearance: none; appearance: none; cursor: ew-resize;}@supports (-moz-appearance: none){#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]{margin-top: 2px;}}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]::-webkit-slider-thumb{pointer-events: auto; border: 1.5px solid rgba(255, 255, 255, 0.8); background: currentColor; -webkit-appearance: none; -moz-appearance: none; appearance: none; box-shadow: 0px 0px 0px #000000; height: 20px; width: 12px; border-radius: 5px;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range].active::-webkit-slider-thumb{border-color: #01bad2;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]::-moz-range-thumb{pointer-events: auto; border: 1.5px solid rgba(255, 255, 255, 0.8); background: currentColor; box-shadow: 0px 0px 0px #000000; height: 18px; width: 10px; border-radius: 5px; margin-top: 3px;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range].active::-moz-range-thumb{border-color: #01bad2;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]::-ms-thumb{pointer-events: auto; border: 1.5px solid rgba(255, 255, 255, 0.8); background: currentColor; box-shadow: 0px 0px 0px #000000; height: 20px; width: 12px; border-radius: 5px;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range].active::-ms-thumb{border-color: #01bad2;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_remove_color{font-size: 14px !important; text-align: center !important; padding: 0;}@keyframes dropZoneInsert{to{box-shadow: inset 0 0 30px 0 rgba(1, 186, 210, 0.5);}}.oe_drop_zone{background: rgba(1, 186, 210, 0.5); animation: dropZoneInsert 1s linear 0s infinite alternate;}.oe_drop_zone.oe_insert{position: relative; width: 100%; border-radius: 0.3rem; outline: 2px dashed #01bad2; outline-offset: -2px; z-index: 1040;}.oe_drop_zone:not(.oe_grid_zone).oe_insert{min-width: 30px; height: 30px; min-height: 30px; margin: -15px 0; padding: 0;}.oe_drop_zone:not(.oe_grid_zone).oe_insert.oe_vertical{width: 30px; float: left; margin: 0 -15px;}.oe_drop_zone:not(.oe_grid_zone).oe_drop_zone_danger{background-color: rgba(230, 88, 108, 0.15); color: #e6586c; border-color: #e6586c;}#oe_manipulators{position: relative; z-index: 1040; pointer-events: none;}#oe_manipulators .oe_overlay{position: absolute; top: auto; left: auto; bottom: auto; right: auto; display: none; border-color: #01bad2; background: transparent; text-align: center; font-size: 16px; transition: opacity 400ms linear 0s;}#oe_manipulators .oe_overlay.o_overlay_hidden{opacity: 0 !important; transition: none;}#oe_manipulators .oe_overlay.oe_active{display: block; z-index: 1;}#oe_manipulators .oe_overlay > .o_handles{position: absolute; top: -100000px; left: 0; bottom: auto; right: 0; border-color: inherit; pointer-events: auto;}#oe_manipulators .oe_overlay > .o_handles > .o_handle{position: absolute;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_side_y{height: 14px;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_side_x{width: 14px;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.w{inset: 100000px auto -100000px 1px; transform: translateX(-50%); cursor: ew-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.e{inset: 100000px 1px -100000px auto; transform: translateX(50%); cursor: ew-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.n{inset: 100000px 0 auto 0; cursor: ns-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.n.o_grid_handle{transform: translateY(-50%);}#oe_manipulators .oe_overlay > .o_handles > .o_handle.n.o_grid_handle:before{transform: translateY(1px);}#oe_manipulators .oe_overlay > .o_handles > .o_handle.s{inset: auto 0 -100000px 0; cursor: ns-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.s.o_grid_handle{transform: translateY(50%);}#oe_manipulators .oe_overlay > .o_handles > .o_handle.s.o_grid_handle:before{transform: translateY(-1px);}#oe_manipulators .oe_overlay > .o_handles > .o_handle.ne{inset: 100001px 1px auto auto; transform: translate(50%, -50%); cursor: nesw-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.se{inset: auto 1px -99999px auto; transform: translate(50%, 50%); cursor: nwse-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.sw{inset: auto auto -99999px 1px; transform: translate(-50%, 50%); cursor: nesw-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.nw{inset: 100001px auto auto 1px; transform: translate(-50%, -50%); cursor: nwse-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle .o_handle_indicator{position: absolute; inset: -7px; display: block; width: 14px; height: 14px; margin: auto; border: solid 2px #01bad2; border-radius: 14px; background: #FFFFFF; outline: 3px solid #FFFFFF; outline-offset: -7px; transition: all 0.2s ease-in-out;}#oe_manipulators .oe_overlay > .o_handles > .o_handle .o_handle_indicator::before{content: ''; position: absolute; inset: -14px; display: block; border-radius: inherit;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_column_handle.o_side_y{background-color: rgba(1, 186, 210, 0.1);}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_column_handle.o_side_y::after{content: ''; position: absolute; height: 14px;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_column_handle.o_side_y.n{border-bottom: dashed 1px rgba(1, 186, 210, 0.5);}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_column_handle.o_side_y.n::after{inset: 0 0 auto 0; transform: translateY(-50%);}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_column_handle.o_side_y.s{border-top: dashed 1px rgba(1, 186, 210, 0.5);}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_column_handle.o_side_y.s::after{inset: auto 0 0 0; transform: translateY(50%);}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_side::before{content: ''; position: absolute; inset: 0; background: #01bad2;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_side.o_side_x::before{width: 2px; margin: 0 auto;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_side.o_side_y::before{height: 2px; margin: auto 0;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_side.o_column_handle.n::before{margin: 0 auto auto;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_side.o_column_handle.s::before{margin: auto auto 0;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly{cursor: default; pointer-events: none;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly.o_column_handle.o_side_y{border: none; background: none;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly::after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly .o_handle_indicator{display: none;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap{position: absolute; top: 100000px; left: 50%; bottom: auto; right: auto; transform: translate(-50%, -150%);}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap.o_we_hidden_overlay_options{display: none;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options{display: -webkit-box; display: -webkit-flex; display: flex;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button{margin: 0 1px 0; min-width: 22px; padding: 0 3px; color: #FFFFFF;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *.oe_snippet_remove, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *:not(span).oe_snippet_remove, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > *.oe_snippet_remove, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.oe_snippet_remove{background-color: #a05968;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .o_move_handle{cursor: move; width: 30px; height: 22px; background-position: center; background-repeat: no-repeat;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .o_send_back{width: 30px; height: 22px; background-image: url("/web_editor/static/src/img/snippets_options/bring-backward.svg"); background-position: center; background-repeat: no-repeat;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .o_bring_front{width: 30px; height: 22px; background-image: url("/web_editor/static/src/img/snippets_options/bring-forward.svg"); background-position: center; background-repeat: no-repeat;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button{opacity: 0.6;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *:focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *.focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:not(span):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:not(span):focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:not(span).focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button:focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button.focus{opacity: 1;}#oe_manipulators .oe_overlay.o_top_cover > .o_handles > .o_overlay_options_wrap{top: auto; bottom: -100000px; transform: translate(-50%, 110%);}#oe_manipulators .oe_overlay.o_we_overlay_preview{pointer-events: none;}#oe_manipulators .oe_overlay.o_we_overlay_preview > .o_handles > .o_handle::after, #oe_manipulators .oe_overlay.o_we_overlay_preview > .o_handles .o_overlay_options_wrap{display: none;}#oe_manipulators .oe_overlay.o_we_background_position_overlay{background-color: rgba(0, 0, 0, 0.7); pointer-events: auto;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_content{cursor: url(/web/static/img/openhand.cur), grab;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_content .o_we_grabbing{cursor: grabbing;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary{color: #fff; background-color: #71639e; border-color: #71639e;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:hover{color: #fff; background-color: #605486; border-color: #5a4f7e;}.btn-check:focus + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:focus{color: #fff; background-color: #605486; border-color: #5a4f7e; box-shadow: 0 0 0 0.25rem rgba(134, 122, 173, 0.5);}.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:active, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.active, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.dropdown-toggle{color: #fff; background-color: #5a4f7e; border-color: #554a77;}.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:focus, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:active:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.active:focus, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(134, 122, 173, 0.5);}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:disabled, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.disabled{color: #fff; background-color: #71639e; border-color: #71639e;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary{color: #000; background-color: #e6586c; border-color: #e6586c;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:hover{color: #000; background-color: #ea7182; border-color: #e9697b;}.btn-check:focus + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:focus{color: #000; background-color: #ea7182; border-color: #e9697b; box-shadow: 0 0 0 0.25rem rgba(196, 75, 92, 0.5);}.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:active, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.active, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.dropdown-toggle{color: #000; background-color: #eb7989; border-color: #e9697b;}.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:focus, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:active:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.active:focus, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(196, 75, 92, 0.5);}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:disabled, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.disabled{color: #000; background-color: #e6586c; border-color: #e6586c;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_overlay_background > *{display: block !important; top: 0 !important; right: 0 !important; bottom: 0 !important; left: 0 !important; transform: none !important; max-width: unset !important; max-height: unset !important; z-index: 0 !important;}#oe_manipulators .oe_overlay.o_handlers_idle .o_handle:hover .o_handle_indicator, #oe_manipulators .oe_overlay .o_handle:active .o_handle_indicator{outline-color: #01bad2;}#oe_manipulators .oe_overlay.o_handlers_idle .o_corner_handle:hover .o_handle_indicator, #oe_manipulators .oe_overlay .o_corner_handle:active .o_handle_indicator{transform: scale(1.25);}#oe_manipulators .oe_overlay.o_handlers_idle .o_column_handle.o_side_y:hover, #oe_manipulators .oe_overlay .o_column_handle.o_side_y:active{background: repeating-linear-gradient(45deg, rgba(1, 186, 210, 0.1), rgba(1, 186, 210, 0.1) 5px, rgba(1, 164, 185, 0.25) 5px, rgba(1, 164, 185, 0.25) 10px);}#oe_manipulators .oe_overlay.o_handlers_idle .o_side_x:hover::before, #oe_manipulators .oe_overlay .o_side_x:active::before{width: 4px;}#oe_manipulators .oe_overlay.o_handlers_idle .o_side_x:hover .o_handle_indicator, #oe_manipulators .oe_overlay .o_side_x:active .o_handle_indicator{height: 28px;}#oe_manipulators .oe_overlay.o_handlers_idle .o_side_y:hover::before, #oe_manipulators .oe_overlay .o_side_y:active::before{height: 4px;}#oe_manipulators .oe_overlay.o_handlers_idle .o_side_y:hover .o_handle_indicator, #oe_manipulators .oe_overlay .o_side_y:active .o_handle_indicator{width: 28px;}#oe_manipulators .o_edit_menu_popover{pointer-events: auto;}.oe_overlay.o_draggable_dragging .o_handles{display: none;}.nesw-resize-important *{cursor: nesw-resize !important;}.nwse-resize-important *{cursor: nwse-resize !important;}.ns-resize-important *{cursor: ns-resize !important;}.ew-resize-important *{cursor: ew-resize !important;}.move-important *{cursor: move !important;}.dropdown-menu label .o_switch{margin: 0; padding: 2px 0;}.text-input-group{position: relative; margin-bottom: 45px;}.text-input-group input{font-size: 18px; padding: 10px 10px 10px 5px; display: block; width: 300px; border: none; border-bottom: 1px solid #757575;}.text-input-group input:focus{outline: none;}.text-input-group label{color: #999; font-size: 18px; font-weight: normal; position: absolute; top: 10px; left: 5px; bottom: auto; right: auto; pointer-events: none; transition: 0.2s ease all;}.text-input-group input:focus ~ label, .text-input-group input:valid ~ label{top: -20px; font-size: 14px; color: #5264AE;}.text-input-group .bar{position: relative; display: block; width: 300px;}.text-input-group .bar:before, .text-input-group .bar:after{content: ''; height: 2px; width: 0; bottom: 1px; position: absolute; top: auto; left: auto; bottom: auto; right: auto; background: #5264AE; transition: 0.2s ease all;}.text-input-group .bar:before{left: 50%;}.text-input-group .bar:after{right: 50%;}.text-input-group input:focus ~ .bar:before, .text-input-group input:focus ~ .bar:after{width: 50%;}.text-input-group .highlight{position: absolute; top: 25%; left: 0; bottom: auto; right: auto; height: 60%; width: 100px; pointer-events: none; opacity: 0.5;}.text-input-group input:focus ~ .highlight{animation: inputHighlighter 0.3s ease;}.oe_snippet_body{opacity: 0; animation: fadeInDownSmall 700ms forwards;}.o_container_preview{outline: 2px dashed #01bad2;}.o_we_shape_animated_label{position: absolute; top: 0; left: auto; bottom: auto; right: 0; padding: 0 4px; background: #40ad67; color: white;}.o_we_shape_animated_label > span{display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; max-width: 0;}we-button:hover .o_we_shape_animated_label i{padding-right: 4px;}we-button:hover .o_we_shape_animated_label > span{max-width: 144px; transition: max-width 0.5s ease 0s;}.o_we_ui_loading{position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: 1041; background-color: rgba(0, 0, 0, 0.2); color: #FFFFFF;}#oe_manipulators > .o_we_ui_loading{position: fixed;}.o_we_force_no_transition{transition: none !important;}we-button.o_grid{min-width: fit-content; padding-left: 4.5px !important; padding-right: 4.5px !important;}we-select.o_grid we-toggler{width: fit-content !important;}.o_we_background_grid{padding: 0 !important;}.o_we_background_grid .o_we_cell{fill: #FFFFFF; fill-opacity: .1; stroke: #000000; stroke-opacity: .2; stroke-width: 1px; filter: drop-shadow(-1px -1px 0px rgba(255, 255, 255, 0.3));}.o_we_background_grid.o_we_grid_preview{pointer-events: none;}@media (max-width: 991.98px){.o_we_background_grid.o_we_grid_preview{height: 0;}}.o_we_background_grid.o_we_grid_preview .o_we_cell{animation: gridPreview 2s 0.5s;}@keyframes gridPreview{to{fill-opacity: 0; stroke-opacity: 0;}}.o_we_drag_helper{padding: 0; border: 4px solid #01bad2; border-radius: 4px;}@keyframes highlightPadding{from{border: solid rgba(1, 186, 210, 0.2); border-width: var(--grid-item-padding-y) var(--grid-item-padding-x);}to{border: solid rgba(1, 186, 210, 0); border-width: var(--grid-item-padding-y) var(--grid-item-padding-x);}}.o_we_padding_highlight.o_grid_item{position: relative;}.o_we_padding_highlight.o_grid_item::after{content: ""; position: absolute; top: 0; left: 0; bottom: 0; right: 0; animation: highlightPadding 2s; pointer-events: none;}