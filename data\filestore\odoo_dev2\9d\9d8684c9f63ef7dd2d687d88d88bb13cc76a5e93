

/* ## CSS error message ##*/
body::before {
  font-weight: bold;
  content: "A css error occured, using an old style to render this page";
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 100000000000;
  background-color: #C00;
  color: #DDD;
}

css_error_message {
  content: "Error: Undefined variable: \"$o-gray-100\".\A        on line 2:32 of stdin\A>> $o-webclient-background-color: $o-gray-100 !default;
\A   -------------------------------^\AThis error occurred while compiling the bundle 'web._assets_secondary_variables' containing:\A    - /web/static/src/scss/secondary_variables.scss\A    - /web_editor/static/src/scss/secondary_variables.scss";
}
