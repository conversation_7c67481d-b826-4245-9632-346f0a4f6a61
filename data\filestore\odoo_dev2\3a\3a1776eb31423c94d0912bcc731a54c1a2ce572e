

/* ## CSS error message ##*/
body::before {
  font-weight: bold;
  content: "A css error occured, using an old style to render this page";
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 100000000000;
  background-color: #C00;
  color: #DDD;
}

css_error_message {
  content: "Error: Undefined variable: \"$box-shadow\".\A        on line 60:15 of d:/vm_odoo/odoo/addons/web/static/lib/bootstrap/scss/_utilities.scss\A        from line 21:9 of stdin\A>>         null: $box-shadow,
\A   --------------^\AThis error occurred while compiling the bundle 'web._assets_bootstrap' containing:\A    - /web/static/src/scss/import_bootstrap.scss\A    - /web/static/src/scss/helpers_backport.scss\A    - /web/static/src/scss/utilities_custom.scss\A    - /web/static/lib/bootstrap/scss/utilities/_api.scss\A    - /web/static/src/scss/bootstrap_review.scss";
}
