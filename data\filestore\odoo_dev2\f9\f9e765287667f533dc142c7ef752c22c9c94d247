
/* /web_editor/static/src/components/history_dialog/history_dialog.js */
odoo.define('@web_editor/components/history_dialog/history_dialog',['@web/core/dialog/dialog','@web/core/l10n/dates','@web/core/utils/hooks','@web/core/utils/functions','@odoo/owl','@web/core/l10n/translation'],function(require){'use strict';let __exports={};const{Dialog}=require('@web/core/dialog/dialog');const{formatDateTime}=require('@web/core/l10n/dates');const{useService}=require('@web/core/utils/hooks');const{memoize}=require('@web/core/utils/functions');const{Component,onMounted,useState,markup}=require('@odoo/owl');const{_t}=require('@web/core/l10n/translation');const{DateTime}=luxon;class HistoryDialog extends Component{static template='web_editor.HistoryDialog';static components={Dialog};static props={recordId:Number,recordModel:String,close:Function,restoreRequested:Function,historyMetadata:Array,versionedFieldName:String};state=useState({revisionsData:[],revisionContent:null,revisionComparison:null,revisionId:null});setup(){this.size='xl';this.title=_t('History');this.orm=useService('orm');this.userService=useService('user');onMounted(()=>this.init());}
async init(){this.state.revisionsData=this.props.historyMetadata;await this.updateCurrentRevision(this.props.historyMetadata[0]['revision_id']);}
async updateCurrentRevision(revisionId){if(this.state.revisionId===revisionId){return;}
this.env.services.ui.block();this.state.revisionId=revisionId;this.state.revisionContent=await this.getRevisionContent(revisionId);this.state.revisionComparison=await this.getRevisionComparison(revisionId);this.env.services.ui.unblock();}
getRevisionComparison=memoize(async function getRevisionComparison(revisionId){const comparison=await this.orm.call(this.props.recordModel,'html_field_history_get_comparison_at_revision',[this.props.recordId,this.props.versionedFieldName,revisionId]);return markup(comparison);}.bind(this));getRevisionContent=memoize(async function getRevisionContent(revisionId){const content=await this.orm.call(this.props.recordModel,'html_field_history_get_content_at_revision',[this.props.recordId,this.props.versionedFieldName,revisionId]);return markup(content);}.bind(this));async _onRestoreRevisionClick(){this.env.services.ui.block();const restoredContent=await this.getRevisionContent(this.state.revisionId);this.props.restoreRequested(restoredContent);this.env.services.ui.unblock();this.props.close();}
getRevisionDate(revision){return formatDateTime(DateTime.fromISO(revision['create_date'],{zone:'utc'}).setZone(this.userService.tz));}}
__exports[Symbol.for("default")]=HistoryDialog;return __exports;});;

/* /web_editor/static/src/components/media_dialog/document_selector.js */
odoo.define('@web_editor/components/media_dialog/document_selector',['@web/core/l10n/translation','@web_editor/components/media_dialog/file_selector'],function(require){'use strict';let __exports={};const{_t}=require("@web/core/l10n/translation");const{Attachment,FileSelector,IMAGE_MIMETYPES}=require("@web_editor/components/media_dialog/file_selector");const DocumentAttachment=__exports.DocumentAttachment=class DocumentAttachment extends Attachment{}
DocumentAttachment.template='web_editor.DocumentAttachment';const DocumentSelector=__exports.DocumentSelector=class DocumentSelector extends FileSelector{setup(){super.setup();this.uploadText=_t("Upload a document");this.urlPlaceholder="https://www.odoo.com/mydocument";this.addText=_t("Add URL");this.searchPlaceholder=_t("Search a document");this.allLoadedText=_t("All documents have been loaded");}
get attachmentsDomain(){const domain=super.attachmentsDomain;domain.push(['mimetype','not in',IMAGE_MIMETYPES]);domain.unshift('&','|',['url','=',null],'!',['url','=like','/web/assets/%']);return domain;}
async onClickDocument(document){this.selectAttachment(document);await this.props.save();}
async fetchAttachments(...args){const attachments=await super.fetchAttachments(...args);if(this.selectInitialMedia()){for(const attachment of attachments){if(`/web/content/${attachment.id}`===this.props.media.getAttribute('href').replace(/[?].*/,'')){this.selectAttachment(attachment);}}}
return attachments;}
static async createElements(selectedMedia,{orm}){return Promise.all(selectedMedia.map(async attachment=>{const linkEl=document.createElement('a');let href=`/web/content/${encodeURIComponent(attachment.id)}?unique=${encodeURIComponent(attachment.checksum)}&download=true`;if(!attachment.public){let accessToken=attachment.access_token;if(!accessToken){[accessToken]=await orm.call('ir.attachment','generate_access_token',[attachment.id],);}
href+=`&access_token=${encodeURIComponent(accessToken)}`;}
linkEl.href=href;linkEl.title=attachment.name;linkEl.dataset.mimetype=attachment.mimetype;return linkEl;}));}}
DocumentSelector.mediaSpecificClasses=['o_image'];DocumentSelector.mediaSpecificStyles=[];DocumentSelector.mediaExtraClasses=[];DocumentSelector.tagNames=['A'];DocumentSelector.attachmentsListTemplate='web_editor.DocumentsListTemplate';DocumentSelector.components={...FileSelector.components,DocumentAttachment,};return __exports;});;

/* /web_editor/static/src/components/media_dialog/file_selector.js */
odoo.define('@web_editor/components/media_dialog/file_selector',['@web/core/l10n/translation','@web/core/utils/hooks','@web/core/confirmation_dialog/confirmation_dialog','@web/core/dialog/dialog','@web/core/utils/concurrency','@web/core/utils/timing','@web_editor/components/media_dialog/search_media','@odoo/owl'],function(require){'use strict';let __exports={};const{_t}=require("@web/core/l10n/translation");const{useService}=require('@web/core/utils/hooks');const{ConfirmationDialog}=require('@web/core/confirmation_dialog/confirmation_dialog');const{Dialog}=require('@web/core/dialog/dialog');const{KeepLast}=require("@web/core/utils/concurrency");const{useDebounced}=require("@web/core/utils/timing");const{SearchMedia}=require("@web_editor/components/media_dialog/search_media");const{Component,xml,useState,useRef,onWillStart,useEffect}=require("@odoo/owl");const IMAGE_MIMETYPES=__exports.IMAGE_MIMETYPES=['image/jpg','image/jpeg','image/jpe','image/png','image/svg+xml','image/gif','image/webp'];const IMAGE_EXTENSIONS=__exports.IMAGE_EXTENSIONS=['.jpg','.jpeg','.jpe','.png','.svg','.gif','.webp'];class RemoveButton extends Component{setup(){this.removeTitle=_t("This file is attached to the current record.");if(this.props.model==='ir.ui.view'){this.removeTitle=_t("This file is a public view attachment.");}}
remove(ev){ev.stopPropagation();this.props.remove();}}
RemoveButton.template=xml`<i class="fa fa-trash o_existing_attachment_remove position-absolute top-0 end-0 p-2 bg-white-25 cursor-pointer opacity-0 opacity-100-hover z-index-1 transition-base" t-att-title="removeTitle" role="img" t-att-aria-label="removeTitle" t-on-click="this.remove"/>`;const AttachmentError=__exports.AttachmentError=class AttachmentError extends Component{setup(){this.title=_t("Alert");}}
AttachmentError.components={Dialog};AttachmentError.template=xml`
<Dialog title="title">
    <div class="form-text">
        <p>The image could not be deleted because it is used in the
            following pages or views:</p>
        <ul t-foreach="props.views"  t-as="view" t-key="view.id">
            <li>
                <a t-att-href="'/web#model=ir.ui.view&amp;id=' + window.encodeURIComponent(view.id)">
                    <t t-esc="view.name"/>
                </a>
            </li>
        </ul>
    </div>
    <t t-set-slot="footer">
        <button class="btn btn-primary" t-on-click="() => this.props.close()">
            Ok
        </button>
    </t>
</Dialog>`;const Attachment=__exports.Attachment=class Attachment extends Component{setup(){this.dialogs=useService('dialog');this.rpc=useService('rpc');}
remove(){this.dialogs.add(ConfirmationDialog,{body:_t("Are you sure you want to delete this file?"),confirm:async()=>{const prevented=await this.rpc('/web_editor/attachment/remove',{ids:[this.props.id],});if(!Object.keys(prevented).length){this.props.onRemoved(this.props.id);}else{this.dialogs.add(AttachmentError,{views:prevented[this.props.id],});}},});}}
Attachment.components={RemoveButton,};const FileSelectorControlPanel=__exports.FileSelectorControlPanel=class FileSelectorControlPanel extends Component{setup(){this.state=useState({showUrlInput:false,urlInput:'',isValidUrl:false,isValidFileFormat:false});this.fileInput=useRef('file-input');}
get showSearchServiceSelect(){return this.props.searchService&&this.props.needle;}
get enableUrlUploadClick(){return!this.state.showUrlInput||(this.state.urlInput&&this.state.isValidUrl&&this.state.isValidFileFormat);}
async onUrlUploadClick(){if(!this.state.showUrlInput){this.state.showUrlInput=true;}else{await this.props.uploadUrl(this.state.urlInput);this.state.urlInput='';}}
onUrlInput(ev){const{isValidUrl,isValidFileFormat}=this.props.validateUrl(ev.target.value);this.state.isValidFileFormat=isValidFileFormat;this.state.isValidUrl=isValidUrl;}
onClickUpload(){this.fileInput.el.click();}
async onChangeFileInput(){const inputFiles=this.fileInput.el.files;if(!inputFiles.length){return;}
await this.props.uploadFiles(inputFiles);const fileInputEl=this.fileInput.el;if(fileInputEl){fileInputEl.value="";}}}
FileSelectorControlPanel.template='web_editor.FileSelectorControlPanel';FileSelectorControlPanel.components={SearchMedia,};const FileSelector=__exports.FileSelector=class FileSelector extends Component{setup(){this.notificationService=useService("notification");this.orm=useService('orm');this.uploadService=useService('upload');this.keepLast=new KeepLast();this.loadMoreButtonRef=useRef('load-more-button');this.existingAttachmentsRef=useRef("existing-attachments");this.state=useState({attachments:[],canScrollAttachments:false,canLoadMoreAttachments:false,isFetchingAttachments:false,needle:'',});this.NUMBER_OF_ATTACHMENTS_TO_DISPLAY=30;onWillStart(async()=>{this.state.attachments=await this.fetchAttachments(this.NUMBER_OF_ATTACHMENTS_TO_DISPLAY,0);});this.debouncedOnScroll=useDebounced(this.updateScroll,15);this.debouncedScrollUpdate=useDebounced(this.updateScroll,500);useEffect((modalEl)=>{if(modalEl){modalEl.addEventListener("scroll",this.debouncedOnScroll);return()=>{modalEl.removeEventListener("scroll",this.debouncedOnScroll);};}},()=>[this.props.modalRef.el?.querySelector("main.modal-body")]);useEffect(()=>{this.loadMoreButtonRef.el.classList.add("o_hide_loading");this.state.canScrollAttachments=false;this.debouncedScrollUpdate();},()=>[this.allAttachments.length]);}
get canLoadMore(){return this.state.canLoadMoreAttachments;}
get hasContent(){return this.state.attachments.length;}
get isFetching(){return this.state.isFetchingAttachments;}
get selectedAttachmentIds(){return this.props.selectedMedia[this.props.id].filter(media=>media.mediaType==='attachment').map(({id})=>id);}
get attachmentsDomain(){const domain=['&',['res_model','=',this.props.resModel],['res_id','=',this.props.resId||0]];domain.unshift('|',['public','=',true]);domain.push(['name','ilike',this.state.needle]);return domain;}
get allAttachments(){return this.state.attachments;}
validateUrl(url){const path=url.split('?')[0];const isValidUrl=/^.+\..+$/.test(path);const isValidFileFormat=true;return{isValidUrl,isValidFileFormat,path};}
async fetchAttachments(limit,offset){this.state.isFetchingAttachments=true;let attachments=[];try{attachments=await this.orm.call('ir.attachment','search_read',[],{domain:this.attachmentsDomain,fields:['name','mimetype','description','checksum','url','type','res_id','res_model','public','access_token','image_src','image_width','image_height','original_id'],order:'id desc',limit,offset,});attachments.forEach(attachment=>attachment.mediaType='attachment');}catch(e){if(e.exceptionName!=='odoo.exceptions.AccessError'){throw e;}}
this.state.canLoadMoreAttachments=attachments.length>=this.NUMBER_OF_ATTACHMENTS_TO_DISPLAY;this.state.isFetchingAttachments=false;return attachments;}
async handleLoadMore(){await this.loadMore();}
async loadMore(){return this.keepLast.add(this.fetchAttachments(this.NUMBER_OF_ATTACHMENTS_TO_DISPLAY,this.state.attachments.length)).then((newAttachments)=>{this.state.attachments.push(...newAttachments);});}
async handleSearch(needle){await this.search(needle);}
async search(needle){this.state.attachments=[];this.state.needle=needle;return this.keepLast.add(this.fetchAttachments(this.NUMBER_OF_ATTACHMENTS_TO_DISPLAY,0)).then((attachments)=>{this.state.attachments=attachments;});}
async uploadFiles(files){await this.uploadService.uploadFiles(files,{resModel:this.props.resModel,resId:this.props.resId},attachment=>this.onUploaded(attachment));}
async uploadUrl(url){await this.uploadService.uploadUrl(url,{resModel:this.props.resModel,resId:this.props.resId,},attachment=>this.onUploaded(attachment));}
async onUploaded(attachment){this.state.attachments=[attachment,...this.state.attachments.filter(attach=>attach.id!==attachment.id)];this.selectAttachment(attachment);if(!this.props.multiSelect){await this.props.save();}
if(this.props.onAttachmentChange){this.props.onAttachmentChange(attachment);}}
onRemoved(attachmentId){this.state.attachments=this.state.attachments.filter(attachment=>attachment.id!==attachmentId);}
selectAttachment(attachment){this.props.selectMedia({...attachment,mediaType:'attachment'});}
selectInitialMedia(){return this.props.media&&this.constructor.tagNames.includes(this.props.media.tagName)&&!this.selectedAttachmentIds.length;}
updateScroll(){const loadMoreTop=this.loadMoreButtonRef.el.getBoundingClientRect().top;const modalEl=this.props.modalRef.el.querySelector("main.modal-body");const modalBottom=modalEl.getBoundingClientRect().bottom;this.state.canScrollAttachments=loadMoreTop>=modalBottom;this.loadMoreButtonRef.el.classList.remove("o_hide_loading");}
isAttachmentHidden(attachmentEl){const attachmentBottom=Math.round(attachmentEl.getBoundingClientRect().bottom);const modalEl=this.props.modalRef.el.querySelector("main.modal-body");const modalBottom=modalEl.getBoundingClientRect().bottom;return attachmentBottom>modalBottom;}
handleScrollAttachments(){let scrollToEl=this.loadMoreButtonRef.el;const attachmentEls=[...this.existingAttachmentsRef.el.querySelectorAll(".o_existing_attachment_cell")];const firstHiddenAttachmentEl=attachmentEls.find(el=>this.isAttachmentHidden(el));if(firstHiddenAttachmentEl){const attachmentBottom=firstHiddenAttachmentEl.getBoundingClientRect().bottom;const attachmentIndex=attachmentEls.indexOf(firstHiddenAttachmentEl);const firstNextRowAttachmentEl=attachmentEls.slice(attachmentIndex).find(el=>{return el.getBoundingClientRect().bottom>attachmentBottom;})
scrollToEl=firstNextRowAttachmentEl||scrollToEl;}
scrollToEl.scrollIntoView({block:"end",inline:"nearest",behavior:"smooth"});}}
FileSelector.template='web_editor.FileSelector';FileSelector.components={FileSelectorControlPanel,};return __exports;});;

/* /web_editor/static/src/components/media_dialog/icon_selector.js */
odoo.define('@web_editor/components/media_dialog/icon_selector',['@web/core/l10n/translation','@web_editor/js/wysiwyg/fonts','@web_editor/components/media_dialog/search_media','@odoo/owl'],function(require){'use strict';let __exports={};const{_t}=require("@web/core/l10n/translation");const fonts=require('@web_editor/js/wysiwyg/fonts')[Symbol.for("default")];const{SearchMedia}=require("@web_editor/components/media_dialog/search_media");const{Component,useState}=require("@odoo/owl");const IconSelector=__exports.IconSelector=class IconSelector extends Component{setup(){this.state=useState({fonts:this.props.fonts,needle:'',});this.searchPlaceholder=_t("Search a pictogram");}
get selectedMediaIds(){return this.props.selectedMedia[this.props.id].map(({id})=>id);}
search(needle){this.state.needle=needle;if(!this.state.needle){this.state.fonts=this.props.fonts;}else{this.state.fonts=this.props.fonts.map(font=>{const icons=font.icons.filter(icon=>icon.alias.indexOf(this.state.needle)>=0);return{...font,icons};});}}
async onClickIcon(font,icon){this.props.selectMedia({...icon,fontBase:font.base,initialIconChanged:this.props.media&&!icon.names.some(name=>this.props.media.classList.contains(name)),});await this.props.save();}
static createElements(selectedMedia){return selectedMedia.map(icon=>{const iconEl=document.createElement('span');iconEl.classList.add(icon.fontBase,icon.names[0]);return iconEl;});}
static initFonts(){fonts.computeFonts();const allFonts=fonts.fontIcons.map(({cssData,base})=>{const uniqueIcons=Array.from(new Map(cssData.map(icon=>{const alias=icon.names.join(',');const id=`${base}_${alias}`;return[id,{...icon,alias,id}];})).values());return{base,icons:uniqueIcons};});return allFonts;}}
IconSelector.mediaSpecificClasses=['fa'];IconSelector.mediaSpecificStyles=['color','background-color'];IconSelector.mediaExtraClasses=['rounded-circle','rounded','img-thumbnail','shadow',/^text-\S+$/,/^bg-\S+$/,/^fa-\S+$/,];IconSelector.tagNames=['SPAN','I'];IconSelector.template='web_editor.IconSelector';IconSelector.components={SearchMedia,};return __exports;});;

/* /web_editor/static/src/components/media_dialog/image_selector.js */
odoo.define('@web_editor/components/media_dialog/image_selector',['@web/core/l10n/translation','@web/core/utils/hooks','@web_editor/js/common/utils','@web_editor/components/media_dialog/file_selector','@web/core/utils/concurrency','@odoo/owl'],function(require){'use strict';let __exports={};const{_t}=require("@web/core/l10n/translation");const{useService}=require('@web/core/utils/hooks');const weUtils=require('@web_editor/js/common/utils')[Symbol.for("default")];const{Attachment,FileSelector,IMAGE_MIMETYPES,IMAGE_EXTENSIONS}=require("@web_editor/components/media_dialog/file_selector");const{KeepLast}=require("@web/core/utils/concurrency");const{useRef,useState,useEffect}=require("@odoo/owl");const AutoResizeImage=__exports.AutoResizeImage=class AutoResizeImage extends Attachment{setup(){super.setup();this.image=useRef('auto-resize-image');this.container=useRef('auto-resize-image-container');this.state=useState({loaded:false,});useEffect(()=>{this.image.el.addEventListener('load',()=>this.onImageLoaded());return this.image.el.removeEventListener('load',()=>this.onImageLoaded());},()=>[]);}
async onImageLoaded(){if(!this.image.el){return;}
if(this.props.onLoaded){await this.props.onLoaded(this.image.el);if(!this.image.el){return;}}
const aspectRatio=this.image.el.offsetWidth/this.image.el.offsetHeight;const width=aspectRatio*this.props.minRowHeight;this.container.el.style.flexGrow=width;this.container.el.style.flexBasis=`${width}px`;this.state.loaded=true;}}
AutoResizeImage.template='web_editor.AutoResizeImage';const ImageSelector=__exports.ImageSelector=class ImageSelector extends FileSelector{setup(){super.setup();this.rpc=useService('rpc');this.keepLastLibraryMedia=new KeepLast();this.state.libraryMedia=[];this.state.libraryResults=null;this.state.isFetchingLibrary=false;this.state.searchService='all';this.state.showOptimized=false;this.NUMBER_OF_MEDIA_TO_DISPLAY=10;this.uploadText=_t("Upload an image");this.urlPlaceholder="https://www.odoo.com/logo.png";this.addText=_t("Add URL");this.searchPlaceholder=_t("Search an image");this.urlWarningTitle=_t("Uploaded image's format is not supported. Try with: "+IMAGE_EXTENSIONS.join(', '));this.allLoadedText=_t("All images have been loaded");this.showOptimizedOption=this.env.debug;this.MIN_ROW_HEIGHT=128;this.fileMimetypes=IMAGE_MIMETYPES.join(',');this.isImageField=!!(this.props.media&&this.props.media.closest("[data-oe-type=image]"))||!!this.env.addFieldImage;}
get canLoadMore(){if(this.state.searchService==='media-library'){return this.state.libraryResults&&this.state.libraryMedia.length<this.state.libraryResults;}
return super.canLoadMore;}
get hasContent(){if(this.state.searchService==='all'){return super.hasContent||!!this.state.libraryMedia.length;}else if(this.state.searchService==='media-library'){return!!this.state.libraryMedia.length;}
return super.hasContent;}
get isFetching(){return super.isFetching||this.state.isFetchingLibrary;}
get selectedMediaIds(){return this.props.selectedMedia[this.props.id].filter(media=>media.mediaType==='libraryMedia').map(({id})=>id);}
get allAttachments(){return[...super.allAttachments,...this.state.libraryMedia];}
get attachmentsDomain(){const domain=super.attachmentsDomain;domain.push(['mimetype','in',IMAGE_MIMETYPES]);if(!this.props.useMediaLibrary){domain.push('|',['url','=',false],'!',['url','=ilike','/web_editor/shape/%']);}
domain.push('!',['name','=like','%.crop']);domain.push('|',['type','=','binary'],'!',['url','=like','/%/static/%']);if(!this.env.debug){const subDomain=[false];const originalId=this.props.media&&this.props.media.dataset.originalId;if(originalId){subDomain.push(originalId);}
domain.push(['original_id','in',subDomain]);}
return domain;}
async uploadFiles(files){await this.uploadService.uploadFiles(files,{resModel:this.props.resModel,resId:this.props.resId,isImage:true},(attachment)=>this.onUploaded(attachment));}
async uploadUrl(url){await fetch(url).then(async result=>{const blob=await result.blob();blob.id=new Date().getTime();blob.name=new URL(url,window.location.href).pathname.split("/").findLast(s=>s);await this.uploadFiles([blob]);}).catch(async()=>{await new Promise(resolve=>{const imageEl=document.createElement("img");imageEl.onerror=()=>{this.notificationService.add(_t("An error occurred while fetching the entered URL."),{title:_t("Error"),sticky:true,});resolve();};imageEl.onload=()=>{const urlPathname=new URL(url,window.location.href).pathname;const imageExtension=IMAGE_EXTENSIONS.find(format=>urlPathname.endsWith(format));if(this.isImageField&&imageExtension===".webp"){this.notificationService.add(_t("You can not replace a field by this image. If you want to use this image, first save it on your computer and then upload it here."),{title:_t("Error"),sticky:true,});return resolve();}
super.uploadUrl(url).then(resolve);};imageEl.src=url;});});}
validateUrl(...args){const{isValidUrl,path}=super.validateUrl(...args);const isValidFileFormat=IMAGE_EXTENSIONS.some(format=>path.endsWith(format));return{isValidFileFormat,isValidUrl};}
isInitialMedia(attachment){if(this.props.media.dataset.originalSrc){return this.props.media.dataset.originalSrc===attachment.image_src;}
return this.props.media.getAttribute('src')===attachment.image_src;}
async fetchAttachments(limit,offset){const attachments=await super.fetchAttachments(limit,offset);if(this.isImageField){for(const attachment of attachments){if(attachment.mimetype==="image/webp"&&await weUtils.isSrcCorsProtected(attachment.image_src)){attachment.unselectable=true;}}}
const primaryColors={};for(let color=1;color<=5;color++){primaryColors[color]=weUtils.getCSSVariableValue('o-color-'+color);}
return attachments.map(attachment=>{if(attachment.image_src.startsWith('/')){const newURL=new URL(attachment.image_src,window.location.origin);if(attachment.image_src.startsWith('/web_editor/shape/')){newURL.searchParams.forEach((value,key)=>{const match=key.match(/^c([1-5])$/);if(match){newURL.searchParams.set(key,primaryColors[match[1]]);}});}else{newURL.searchParams.set('height',2*this.MIN_ROW_HEIGHT);}
attachment.thumbnail_src=newURL.pathname+newURL.search;}
if(this.selectInitialMedia()&&this.isInitialMedia(attachment)){this.selectAttachment(attachment);}
return attachment;});}
async fetchLibraryMedia(offset){if(!this.state.needle){return{media:[],results:null};}
this.state.isFetchingLibrary=true;try{const response=await this.rpc('/web_editor/media_library_search',{'query':this.state.needle,'offset':offset,},{silent:true,});this.state.isFetchingLibrary=false;const media=(response.media||[]).slice(0,this.NUMBER_OF_MEDIA_TO_DISPLAY);media.forEach(record=>record.mediaType='libraryMedia');return{media,results:response.results};}catch{console.error(`Couldn't reach API endpoint.`);this.state.isFetchingLibrary=false;return{media:[],results:null};}}
async loadMore(...args){await super.loadMore(...args);if(!this.props.useMediaLibrary||this.state.searchService!=='media-library'){return;}
return this.keepLastLibraryMedia.add(this.fetchLibraryMedia(this.state.libraryMedia.length)).then(({media})=>{this.state.libraryMedia.push(...media);});}
async search(...args){await super.search(...args);if(!this.props.useMediaLibrary){return;}
if(!this.state.needle){this.state.searchService='all';}
this.state.libraryMedia=[];this.state.libraryResults=0;return this.keepLastLibraryMedia.add(this.fetchLibraryMedia(0)).then(({media,results})=>{this.state.libraryMedia=media;this.state.libraryResults=results;});}
async onClickAttachment(attachment){if(attachment.unselectable){this.notificationService.add(_t("You can not replace a field by this image. If you want to use this image, first save it on your computer and then upload it here."),{title:_t("Error"),sticky:true,});return;}
this.selectAttachment(attachment);if(!this.props.multiSelect){await this.props.save();}}
async onClickMedia(media){this.props.selectMedia({...media,mediaType:'libraryMedia'});if(!this.props.multiSelect){await this.props.save();}}
static async createElements(selectedMedia,{orm,rpc}){const toSave=Object.fromEntries(selectedMedia.filter(media=>media.mediaType==='libraryMedia').map(media=>[media.id,{query:media.query||'',is_dynamic_svg:!!media.isDynamicSVG,dynamic_colors:media.dynamicColors,}]));let savedMedia=[];if(Object.keys(toSave).length!==0){savedMedia=await rpc('/web_editor/save_library_media',{media:toSave});}
const selected=selectedMedia.filter(media=>media.mediaType==='attachment').concat(savedMedia).map(attachment=>{if(attachment.image_src&&attachment.image_src.startsWith('/web_editor/shape/')){const colorCustomizedURL=new URL(attachment.image_src,window.location.origin);colorCustomizedURL.searchParams.forEach((value,key)=>{const match=key.match(/^c([1-5])$/);if(match){colorCustomizedURL.searchParams.set(key,weUtils.getCSSVariableValue(`o-color-${match[1]}`));}});attachment.image_src=colorCustomizedURL.pathname+colorCustomizedURL.search;}
return attachment;});return Promise.all(selected.map(async(attachment)=>{const imageEl=document.createElement('img');let src=attachment.image_src;if(!attachment.public&&!attachment.url){let accessToken=attachment.access_token;if(!accessToken){[accessToken]=await orm.call('ir.attachment','generate_access_token',[attachment.id],);}
src+=`?access_token=${encodeURIComponent(accessToken)}`;}
imageEl.src=src;imageEl.alt=attachment.description||'';return imageEl;}));}
async onImageLoaded(imgEl,attachment){this.debouncedScrollUpdate();if(attachment.mediaType==='libraryMedia'&&!imgEl.src.startsWith('blob')){await this.onLibraryImageLoaded(imgEl,attachment);}}
async onLibraryImageLoaded(imgEl,media){const mediaUrl=imgEl.src;try{const response=await fetch(mediaUrl);const contentType=response.headers.get("content-type");if(contentType&&contentType.startsWith("image/svg+xml")){let svg=await response.text();const dynamicColors={};const combinedColorsRegex=new RegExp(Object.values(weUtils.DEFAULT_PALETTE).join('|'),'gi');svg=svg.replace(combinedColorsRegex,match=>{const colorId=Object.keys(weUtils.DEFAULT_PALETTE).find(key=>weUtils.DEFAULT_PALETTE[key]===match.toUpperCase());const colorKey='c'+colorId
dynamicColors[colorKey]=weUtils.getCSSVariableValue('o-color-'+colorId);return dynamicColors[colorKey];});const fileName=mediaUrl.split('/').pop();const file=new File([svg],fileName,{type:"image/svg+xml",});imgEl.src=URL.createObjectURL(file);if(Object.keys(dynamicColors).length){media.isDynamicSVG=true;media.dynamicColors=dynamicColors;}}}catch{console.error('CORS is misconfigured on the API server, image will be treated as non-dynamic.');}}}
ImageSelector.mediaSpecificClasses=['img','img-fluid','o_we_custom_image'];ImageSelector.mediaSpecificStyles=[];ImageSelector.mediaExtraClasses=['rounded-circle','rounded','img-thumbnail','shadow','w-25','w-50','w-75','w-100',];ImageSelector.tagNames=['IMG'];ImageSelector.attachmentsListTemplate='web_editor.ImagesListTemplate';ImageSelector.components={...FileSelector.components,AutoResizeImage,};return __exports;});;

/* /web_editor/static/src/components/media_dialog/media_dialog.js */
odoo.define('@web_editor/components/media_dialog/media_dialog',['@web/core/l10n/translation','@web/core/utils/hooks','@web/core/utils/concurrency','@web/core/dialog/dialog','@web/core/notebook/notebook','@web_editor/components/media_dialog/image_selector','@web_editor/components/media_dialog/document_selector','@web_editor/components/media_dialog/icon_selector','@web_editor/components/media_dialog/video_selector','@odoo/owl'],function(require){'use strict';let __exports={};const{_t}=require("@web/core/l10n/translation");const{useService,useChildRef}=require('@web/core/utils/hooks');const{Mutex}=require("@web/core/utils/concurrency");const{Dialog}=require('@web/core/dialog/dialog');const{Notebook}=require('@web/core/notebook/notebook');const{ImageSelector}=require("@web_editor/components/media_dialog/image_selector");const{DocumentSelector}=require("@web_editor/components/media_dialog/document_selector");const{IconSelector}=require("@web_editor/components/media_dialog/icon_selector");const{VideoSelector}=require("@web_editor/components/media_dialog/video_selector");const{Component,useState,useRef,useEffect}=require("@odoo/owl");const TABS=__exports.TABS={IMAGES:{id:'IMAGES',title:_t("Images"),Component:ImageSelector,},DOCUMENTS:{id:'DOCUMENTS',title:_t("Documents"),Component:DocumentSelector,},ICONS:{id:'ICONS',title:_t("Icons"),Component:IconSelector,},VIDEOS:{id:'VIDEOS',title:_t("Videos"),Component:VideoSelector,},};const MediaDialog=__exports.MediaDialog=class MediaDialog extends Component{setup(){this.size='xl';this.contentClass='o_select_media_dialog h-100';this.title=_t("Select a media");this.modalRef=useChildRef();this.rpc=useService('rpc');this.orm=useService('orm');this.notificationService=useService('notification');this.mutex=new Mutex();this.tabs=[];this.selectedMedia=useState({});this.addButtonRef=useRef('add-button');this.initialIconClasses=[];this.addTabs();this.errorMessages={};this.state=useState({activeTab:this.initialActiveTab,});useEffect((nbSelectedAttachments)=>{this.addButtonRef.el.toggleAttribute("disabled",!nbSelectedAttachments);},()=>[this.selectedMedia[this.state.activeTab].length]);}
get initialActiveTab(){if(this.props.activeTab){return this.props.activeTab;}
if(this.props.media){const correspondingTab=Object.keys(TABS).find(id=>TABS[id].Component.tagNames.includes(this.props.media.tagName));if(correspondingTab){return correspondingTab;}}
return this.tabs[0].id;}
addTab(tab,additionalProps={}){this.selectedMedia[tab.id]=[];this.tabs.push({...tab,props:{...tab.props,...additionalProps,id:tab.id,resModel:this.props.resModel,resId:this.props.resId,media:this.props.media,multiImages:this.props.multiImages,selectedMedia:this.selectedMedia,selectMedia:(...args)=>this.selectMedia(...args,tab.id,additionalProps.multiSelect),save:this.save.bind(this),onAttachmentChange:this.props.onAttachmentChange,errorMessages:(errorMessage)=>this.errorMessages[tab.id]=errorMessage,modalRef:this.modalRef,},});}
addTabs(){const onlyImages=this.props.onlyImages||this.props.multiImages||(this.props.media&&this.props.media.parentElement&&(this.props.media.parentElement.dataset.oeField==='image'||this.props.media.parentElement.dataset.oeType==='image'));const noDocuments=onlyImages||this.props.noDocuments;const noIcons=onlyImages||this.props.noIcons;const noVideos=onlyImages||this.props.noVideos;if(!this.props.noImages){this.addTab(TABS.IMAGES,{useMediaLibrary:this.props.useMediaLibrary,multiSelect:this.props.multiImages,});}
if(!noDocuments){this.addTab(TABS.DOCUMENTS);}
if(!noIcons){const fonts=TABS.ICONS.Component.initFonts();this.addTab(TABS.ICONS,{fonts,});if(this.props.media&&TABS.ICONS.Component.tagNames.includes(this.props.media.tagName)){const classes=this.props.media.className.split(/\s+/);const mediaFont=fonts.find(font=>classes.includes(font.base));if(mediaFont){const selectedIcon=mediaFont.icons.find(icon=>icon.names.some(name=>classes.includes(name)));if(selectedIcon){this.initialIconClasses.push(...selectedIcon.names);this.selectMedia(selectedIcon,TABS.ICONS.id);}}}}
if(!noVideos){this.addTab(TABS.VIDEOS,{vimeoPreviewIds:this.props.vimeoPreviewIds,isForBgVideo:this.props.isForBgVideo,});}}
async renderMedia(selectedMedia){const elements=await this.mutex.exec(async()=>await TABS[this.state.activeTab].Component.createElements(selectedMedia,{rpc:this.rpc,orm:this.orm}));elements.forEach(element=>{if(this.props.media){element.classList.add(...this.props.media.classList);const style=this.props.media.getAttribute('style');if(style){element.setAttribute('style',style);}
if(this.state.activeTab===TABS.IMAGES.id){if(this.props.media.dataset.shape){element.dataset.shape=this.props.media.dataset.shape;}
if(this.props.media.dataset.shapeColors){element.dataset.shapeColors=this.props.media.dataset.shapeColors;}
if(this.props.media.dataset.shapeFlip){element.dataset.shapeFlip=this.props.media.dataset.shapeFlip;}
if(this.props.media.dataset.shapeRotate){element.dataset.shapeRotate=this.props.media.dataset.shapeRotate;}
if(this.props.media.dataset.hoverEffect){element.dataset.hoverEffect=this.props.media.dataset.hoverEffect;}
if(this.props.media.dataset.hoverEffectColor){element.dataset.hoverEffectColor=this.props.media.dataset.hoverEffectColor;}
if(this.props.media.dataset.hoverEffectStrokeWidth){element.dataset.hoverEffectStrokeWidth=this.props.media.dataset.hoverEffectStrokeWidth;}
if(this.props.media.dataset.hoverEffectIntensity){element.dataset.hoverEffectIntensity=this.props.media.dataset.hoverEffectIntensity;}}else if([TABS.VIDEOS.id,TABS.DOCUMENTS.id].includes(this.state.activeTab)){const parentEl=this.props.media.parentElement;if(parentEl&&parentEl.tagName==="A"&&parentEl.children.length===1&&this.props.media.tagName==="IMG"){parentEl.replaceWith(parentEl.firstElementChild);}}}
for(const otherTab of Object.keys(TABS).filter(key=>key!==this.state.activeTab)){for(const property of TABS[otherTab].Component.mediaSpecificStyles){element.style.removeProperty(property);}
element.classList.remove(...TABS[otherTab].Component.mediaSpecificClasses);const extraClassesToRemove=[];for(const name of TABS[otherTab].Component.mediaExtraClasses){if(typeof(name)==='string'){extraClassesToRemove.push(name);}else{for(const className of element.classList){if(className.match(name)){extraClassesToRemove.push(className);}}}}
element.classList.remove(...extraClassesToRemove.filter(candidateName=>{for(const name of TABS[this.state.activeTab].Component.mediaExtraClasses){if(typeof(name)==='string'){if(candidateName===name){return false;}}else{for(const className of element.classList){if(className.match(candidateName)){return false;}}}}
return true;}));}
element.classList.remove(...this.initialIconClasses);element.classList.remove('o_modified_image_to_save');element.classList.remove('oe_edited_link');element.classList.add(...TABS[this.state.activeTab].Component.mediaSpecificClasses);});return elements;}
selectMedia(media,tabId,multiSelect){if(multiSelect){const isMediaSelected=this.selectedMedia[tabId].map(({id})=>id).includes(media.id);if(!isMediaSelected){this.selectedMedia[tabId].push(media);}else{this.selectedMedia[tabId]=this.selectedMedia[tabId].filter(m=>m.id!==media.id);}}else{this.selectedMedia[tabId]=[media];}}
async save(){if(this.errorMessages[this.state.activeTab]){this.notificationService.add(this.errorMessages[this.state.activeTab],{type:'danger',});return;}
const selectedMedia=this.selectedMedia[this.state.activeTab];const saveSelectedMedia=selectedMedia.length&&(this.state.activeTab!==TABS.ICONS.id||selectedMedia[0].initialIconChanged||!this.props.media);if(saveSelectedMedia){const elements=await this.renderMedia(selectedMedia);if(this.props.multiImages){await this.props.save(elements);}else{await this.props.save(elements[0]);}}
this.props.close();}
onTabChange(tab){this.state.activeTab=tab;}}
MediaDialog.template='web_editor.MediaDialog';MediaDialog.defaultProps={useMediaLibrary:true,};MediaDialog.components={...Object.keys(TABS).map(key=>TABS[key].Component),Dialog,Notebook,};return __exports;});;

/* /web_editor/static/src/components/media_dialog/search_media.js */
odoo.define('@web_editor/components/media_dialog/search_media',['@web/core/utils/timing','@web/core/utils/hooks','@odoo/owl'],function(require){'use strict';let __exports={};const{useDebounced}=require('@web/core/utils/timing');const{useAutofocus}=require('@web/core/utils/hooks');const{Component,xml,useEffect,useState}=require("@odoo/owl");const SearchMedia=__exports.SearchMedia=class SearchMedia extends Component{setup(){useAutofocus();this.debouncedSearch=useDebounced(this.props.search,1000);this.state=useState({input:this.props.needle||'',});useEffect((input)=>{if(this.hasRendered){this.debouncedSearch(input);}else{this.hasRendered=true;}},()=>[this.state.input]);}}
SearchMedia.template=xml`
<div class="position-relative mw-lg-25 flex-grow-1 me-auto">
    <input type="text" class="o_we_search o_input form-control" t-att-placeholder="props.searchPlaceholder.trim()" t-model="state.input" t-ref="autofocus"/>
    <i class="oi oi-search input-group-text position-absolute end-0 top-50 me-n3 px-2 py-1 translate-middle bg-transparent border-0" title="Search" role="img" aria-label="Search"/>
</div>`;return __exports;});;

/* /web_editor/static/src/components/media_dialog/video_selector.js */
odoo.define('@web_editor/components/media_dialog/video_selector',['@web/core/l10n/translation','@web/core/utils/hooks','@web/core/utils/timing','@odoo/owl'],function(require){'use strict';let __exports={};const{_t}=require("@web/core/l10n/translation");const{useAutofocus,useService}=require('@web/core/utils/hooks');const{debounce}=require('@web/core/utils/timing');const{Component,useState,useRef,onMounted,onWillStart}=require("@odoo/owl");class VideoOption extends Component{}
VideoOption.template='web_editor.VideoOption';class VideoIframe extends Component{static template='web_editor.VideoIframe';static props={src:{type:String},};}
const VideoSelector=__exports.VideoSelector=class VideoSelector extends Component{setup(){this.rpc=useService('rpc');this.http=useService('http');this.PLATFORMS={youtube:'youtube',dailymotion:'dailymotion',vimeo:'vimeo',youku:'youku',};this.OPTIONS={autoplay:{label:_t("Autoplay"),description:_t("Videos are muted when autoplay is enabled"),platforms:[this.PLATFORMS.youtube,this.PLATFORMS.dailymotion,this.PLATFORMS.vimeo],urlParameter:'autoplay=1',},loop:{label:_t("Loop"),platforms:[this.PLATFORMS.youtube,this.PLATFORMS.vimeo],urlParameter:'loop=1',},hide_controls:{label:_t("Hide player controls"),platforms:[this.PLATFORMS.youtube,this.PLATFORMS.dailymotion,this.PLATFORMS.vimeo],urlParameter:'controls=0',},hide_fullscreen:{label:_t("Hide fullscreen button"),platforms:[this.PLATFORMS.youtube],urlParameter:'fs=0',isHidden:()=>this.state.options.filter(option=>option.id==='hide_controls')[0].value,},hide_dm_logo:{label:_t("Hide Dailymotion logo"),platforms:[this.PLATFORMS.dailymotion],urlParameter:'ui-logo=0',},hide_dm_share:{label:_t("Hide sharing button"),platforms:[this.PLATFORMS.dailymotion],urlParameter:'sharing-enable=0',},};this.state=useState({options:[],src:'',urlInput:'',platform:null,vimeoPreviews:[],errorMessage:'',});this.urlInputRef=useRef('url-input');onWillStart(async()=>{if(this.props.media){const src=this.props.media.dataset.oeExpression||this.props.media.dataset.src||(this.props.media.tagName==='IFRAME'&&this.props.media.getAttribute('src'))||'';if(src){this.state.urlInput=src;await this.updateVideo();this.state.options=this.state.options.map((option)=>{const{urlParameter}=this.OPTIONS[option.id];return{...option,value:src.indexOf(urlParameter)>=0};});}}});onMounted(async()=>this.prepareVimeoPreviews());useAutofocus();this.onChangeUrl=debounce((ev)=>this.updateVideo(ev.target.value),500);}
get shownOptions(){if(this.props.isForBgVideo){return[];}
return this.state.options.filter(option=>!this.OPTIONS[option.id].isHidden||!this.OPTIONS[option.id].isHidden());}
async onChangeOption(optionId){this.state.options=this.state.options.map(option=>{if(option.id===optionId){return{...option,value:!option.value};}
return option;});await this.updateVideo();}
async onClickSuggestion(src){this.state.urlInput=src;await this.updateVideo();}
async updateVideo(){if(!this.state.urlInput){this.state.src='';this.state.urlInput='';this.state.options=[];this.state.platform=null;this.state.errorMessage='';this.props.selectMedia({});return;}
const embedMatch=this.state.urlInput.match(/(src|href)=["']?([^"']+)?/);if(embedMatch&&embedMatch[2].length>0&&embedMatch[2].indexOf('instagram')){embedMatch[1]=embedMatch[2];}
const url=embedMatch?embedMatch[1]:this.state.urlInput;const options={};if(this.props.isForBgVideo){Object.keys(this.OPTIONS).forEach(key=>{options[key]=true;});}else{for(const option of this.shownOptions){options[option.id]=option.value;}}
const{embed_url:src,video_id:videoId,params,platform}=await this._getVideoURLData(url,options);if(!src){this.state.errorMessage=_t("The provided url is not valid");}else if(!platform){this.state.errorMessage=_t("The provided url does not reference any supported video");}else{this.state.errorMessage='';}
this.props.errorMessages(this.state.errorMessage);const newOptions=[];if(platform&&platform!==this.state.platform){Object.keys(this.OPTIONS).forEach(key=>{if(this.OPTIONS[key].platforms.includes(platform)){const{label,description}=this.OPTIONS[key];newOptions.push({id:key,label,description});}});}
this.state.src=src;this.props.selectMedia({id:src,src,platform,videoId,params});if(platform!==this.state.platform){this.state.platform=platform;this.state.options=newOptions;}}
async _getVideoURLData(url,options){return await this.rpc('/web_editor/video_url/data',{video_url:url,...options,});}
static createElements(selectedMedia){return selectedMedia.map(video=>{const div=document.createElement('div');div.dataset.oeExpression=video.src;div.innerHTML=`
                <div class="css_editable_mode_display"></div>
                <div class="media_iframe_video_size" contenteditable="false"></div>
                <iframe frameborder="0" contenteditable="false" allowfullscreen="allowfullscreen"></iframe>
            `;div.querySelector('iframe').src=video.src;return div;});}
async prepareVimeoPreviews(){return Promise.all(this.props.vimeoPreviewIds.map(async(videoId)=>{try{const{thumbnail_url:thumbnailSrc}=await this.http.get(`https://vimeo.com/api/oembed.json?url=http%3A//vimeo.com/${encodeURIComponent(videoId)}`);this.state.vimeoPreviews.push({id:videoId,thumbnailSrc,src:`https://player.vimeo.com/video/${encodeURIComponent(videoId)}`});}catch(err){console.warn(`Could not get video #${videoId} from vimeo: ${err}`);}}));}}
VideoSelector.mediaSpecificClasses=['media_iframe_video'];VideoSelector.mediaSpecificStyles=[];VideoSelector.mediaExtraClasses=[];VideoSelector.tagNames=['IFRAME','DIV'];VideoSelector.template='web_editor.VideoSelector';VideoSelector.components={VideoIframe,VideoOption,};VideoSelector.defaultProps={vimeoPreviewIds:[],isForBgVideo:false,};return __exports;});;

/* /web_editor/static/src/components/upload_progress_toast/upload_progress_toast.js */
odoo.define('@web_editor/components/upload_progress_toast/upload_progress_toast',['@web/core/utils/hooks','@odoo/owl'],function(require){'use strict';let __exports={};const{useService}=require('@web/core/utils/hooks');const{Component,useState}=require("@odoo/owl");const ProgressBar=__exports.ProgressBar=class ProgressBar extends Component{get progress(){return Math.round(this.props.progress);}}
ProgressBar.template='web_editor.ProgressBar';ProgressBar.props={progress:{type:Number,optional:true},hasError:{type:Boolean,optional:true},uploaded:{type:Boolean,optional:true},name:String,size:{type:String,optional:true},errorMessage:{type:String,optional:true},};ProgressBar.defaultProps={progress:0,hasError:false,uploaded:false,size:"",errorMessage:"",};const UploadProgressToast=__exports.UploadProgressToast=class UploadProgressToast extends Component{setup(){this.uploadService=useService('upload');this.state=useState(this.uploadService.progressToast);}}
UploadProgressToast.template='web_editor.UploadProgressToast';UploadProgressToast.components={ProgressBar};UploadProgressToast.props={close:Function,};return __exports;});;

/* /web_editor/static/src/components/upload_progress_toast/upload_service.js */
odoo.define('@web_editor/components/upload_progress_toast/upload_service',['@web/core/registry','@web_editor/components/upload_progress_toast/upload_progress_toast','@web/core/l10n/translation','@web/core/utils/files','@web/core/utils/numbers','@web/core/utils/urls','@web/core/utils/strings','@odoo/owl'],function(require){'use strict';let __exports={};const{registry}=require('@web/core/registry');const{UploadProgressToast}=require("@web_editor/components/upload_progress_toast/upload_progress_toast");const{_t}=require("@web/core/l10n/translation");const{checkFileSize}=require("@web/core/utils/files");const{humanNumber}=require("@web/core/utils/numbers");const{getDataURLFromFile}=require("@web/core/utils/urls");const{sprintf}=require("@web/core/utils/strings");const{reactive}=require("@odoo/owl");const AUTOCLOSE_DELAY=__exports.AUTOCLOSE_DELAY=3000;const uploadService=__exports.uploadService={dependencies:['rpc','notification'],start(env,{rpc,notification}){let fileId=0;const progressToast=reactive({files:{},isVisible:false,});registry.category('main_components').add('UploadProgressToast',{Component:UploadProgressToast,props:{close:()=>progressToast.isVisible=false,}});const addFile=(file)=>{progressToast.files[file.id]=file;progressToast.isVisible=true;return progressToast.files[file.id];};const deleteFile=(fileId)=>{delete progressToast.files[fileId];if(!Object.keys(progressToast.files).length){progressToast.isVisible=false;}};return{get progressToast(){return progressToast;},get fileId(){return fileId;},addFile,deleteFile,incrementId(){fileId++;},uploadUrl:async(url,{resModel,resId},onUploaded)=>{const attachment=await rpc('/web_editor/attachment/add_url',{url,'res_model':resModel,'res_id':resId,});await onUploaded(attachment);},uploadFiles:async(files,{resModel,resId,isImage},onUploaded)=>{const sortedFiles=Array.from(files).sort((a,b)=>a.size-b.size);for(const file of sortedFiles){let fileSize=file.size;if(!checkFileSize(fileSize,notification)){return null;}
if(!fileSize){fileSize="";}else{fileSize=humanNumber(fileSize)+"B";}
const id=++fileId;file.progressToastId=id;addFile({id,name:file.name,size:fileSize,});}
for(const sortedFile of sortedFiles){const file=progressToast.files[sortedFile.progressToastId];let dataURL;try{dataURL=await getDataURLFromFile(sortedFile);}catch{deleteFile(file.id);env.services.notification.add(sprintf(_t('Could not load the file "%s".'),sortedFile.name),{type:'danger'});continue}
try{const xhr=new XMLHttpRequest();xhr.upload.addEventListener('progress',ev=>{const rpcComplete=ev.loaded/ev.total*100;file.progress=rpcComplete;});xhr.upload.addEventListener('load',function(){file.progress=100;});const attachment=await rpc('/web_editor/attachment/add_data',{'name':file.name,'data':dataURL.split(',')[1],'res_id':resId,'res_model':resModel,'is_image':!!isImage,'width':0,'quality':0,},{xhr});if(attachment.error){file.hasError=true;file.errorMessage=attachment.error;}else{if(attachment.mimetype==='image/webp'){const image=document.createElement('img');image.src=`data:image/webp;base64,${dataURL.split(',')[1]}`;await new Promise(resolve=>image.addEventListener('load',resolve));const canvas=document.createElement('canvas');canvas.width=image.width;canvas.height=image.height;const ctx=canvas.getContext('2d');ctx.fillStyle='rgb(255, 255, 255)';ctx.fillRect(0,0,canvas.width,canvas.height);ctx.drawImage(image,0,0);const altDataURL=canvas.toDataURL('image/jpeg',0.75);await rpc('/web_editor/attachment/add_data',{'name':file.name.replace(/\.webp$/,'.jpg'),'data':altDataURL.split(',')[1],'res_id':attachment.id,'res_model':'ir.attachment','is_image':true,'width':0,'quality':0,},{xhr});}
file.uploaded=true;await onUploaded(attachment);}
setTimeout(()=>deleteFile(file.id),AUTOCLOSE_DELAY);}catch(error){file.hasError=true;setTimeout(()=>deleteFile(file.id),AUTOCLOSE_DELAY);throw error;}}}};},};registry.category('services').add('upload',uploadService);return __exports;});;

/* /web_unsplash/static/src/components/media_dialog/image_selector.js */
odoo.define('@web_unsplash/components/media_dialog/image_selector',['@web/core/l10n/translation','@web/core/utils/patch','@web/core/utils/concurrency','@web_editor/components/media_dialog/media_dialog','@web_editor/components/media_dialog/image_selector','@web/core/utils/hooks','@web_editor/components/upload_progress_toast/upload_service','@odoo/owl'],function(require){'use strict';let __exports={};const{_t}=require("@web/core/l10n/translation");const{patch}=require("@web/core/utils/patch");const{KeepLast}=require("@web/core/utils/concurrency");const{MediaDialog,TABS}=require('@web_editor/components/media_dialog/media_dialog');const{ImageSelector}=require('@web_editor/components/media_dialog/image_selector');const{useService}=require('@web/core/utils/hooks');const{uploadService,AUTOCLOSE_DELAY}=require('@web_editor/components/upload_progress_toast/upload_service');const{useState,Component}=require("@odoo/owl");class UnsplashCredentials extends Component{setup(){this.state=useState({key:'',appId:'',hasKeyError:this.props.hasCredentialsError,hasAppIdError:this.props.hasCredentialsError,});}
submitCredentials(){if(this.state.key===''){this.state.hasKeyError=true;}else if(this.state.appId===''){this.state.hasAppIdError=true;}else{this.props.submitCredentials(this.state.key,this.state.appId);}}}
UnsplashCredentials.template='web_unsplash.UnsplashCredentials';const UnsplashError=__exports.UnsplashError=class UnsplashError extends Component{}
UnsplashError.template='web_unsplash.UnsplashError';UnsplashError.components={UnsplashCredentials,};patch(ImageSelector.prototype,{setup(){super.setup();this.unsplash=useService('unsplash');this.keepLastUnsplash=new KeepLast();this.state.unsplashRecords=[];this.state.isFetchingUnsplash=false;this.state.isMaxed=false;this.state.unsplashError=null;this.state.useUnsplash=true;this.NUMBER_OF_RECORDS_TO_DISPLAY=30;this.errorMessages={'key_not_found':{title:_t("Setup Unsplash to access royalty free photos."),subtitle:"",},401:{title:_t("Unauthorized Key"),subtitle:_t("Please check your Unsplash access key and application ID."),},403:{title:_t("Search is temporarily unavailable"),subtitle:_t("The max number of searches is exceeded. Please retry in an hour or extend to a better account."),},};},get canLoadMore(){if(this.state.searchService==='all'){return super.canLoadMore||this.state.needle&&!this.state.isMaxed&&!this.state.unsplashError;}else if(this.state.searchService==='unsplash'){return this.state.needle&&!this.state.isMaxed&&!this.state.unsplashError;}
return super.canLoadMore;},get hasContent(){if(this.state.searchService==='all'){return super.hasContent||!!this.state.unsplashRecords.length;}else if(this.state.searchService==='unsplash'){return!!this.state.unsplashRecords.length;}
return super.hasContent;},get errorTitle(){if(this.errorMessages[this.state.unsplashError]){return this.errorMessages[this.state.unsplashError].title;}
return _t("Something went wrong");},get errorSubtitle(){if(this.errorMessages[this.state.unsplashError]){return this.errorMessages[this.state.unsplashError].subtitle;}
return _t("Please check your internet connection or contact administrator.");},get selectedRecordIds(){return this.props.selectedMedia[this.props.id].filter(media=>media.mediaType==='unsplashRecord').map(({id})=>id);},get isFetching(){return super.isFetching||this.state.isFetchingUnsplash;},get combinedRecords(){function alternate(a,b){return[a.map((v,i)=>i<b.length?[v,b[i]]:v),b.slice(a.length),].flat(2);}
return alternate(this.state.unsplashRecords,this.state.libraryMedia);},get allAttachments(){return[...super.allAttachments,...this.state.unsplashRecords];},set canLoadMore(_){},set hasContent(_){},set isFetching(_){},set selectedMediaIds(_){},set attachmentsDomain(_){},set errorTitle(_){},set errorSubtitle(_){},set selectedRecordIds(_){},async fetchUnsplashRecords(offset){if(!this.state.needle){return{records:[],isMaxed:false};}
this.state.isFetchingUnsplash=true;try{const{isMaxed,images}=await this.unsplash.getImages(this.state.needle,offset,this.NUMBER_OF_RECORDS_TO_DISPLAY,this.props.orientation);this.state.isFetchingUnsplash=false;this.state.unsplashError=false;const existingIds=this.state.unsplashRecords.map(existing=>existing.id);const newImages=images.filter(record=>!existingIds.includes(record.id));const records=newImages.map(record=>{const url=new URL(record.urls.regular);url.searchParams.set('h',2*this.MIN_ROW_HEIGHT);url.searchParams.delete('w');return Object.assign({},record,{url:url.toString(),mediaType:'unsplashRecord',});});return{isMaxed,records};}catch(e){this.state.isFetchingUnsplash=false;if(e==='no_access'){this.state.useUnsplash=false;}else{this.state.unsplashError=e;}
return{records:[],isMaxed:true};}},async loadMore(...args){await super.loadMore(...args);return this.keepLastUnsplash.add(this.fetchUnsplashRecords(this.state.unsplashRecords.length)).then(({records,isMaxed})=>{this.state.unsplashRecords.push(...records);this.state.isMaxed=isMaxed;});},async search(...args){await super.search(...args);await this.searchUnsplash();},async searchUnsplash(){if(!this.state.needle){this.state.unsplashError=false;this.state.unsplashRecords=[];this.state.isMaxed=false;}
return this.keepLastUnsplash.add(this.fetchUnsplashRecords(0)).then(({records,isMaxed})=>{this.state.unsplashRecords=records;this.state.isMaxed=isMaxed;});},async onClickRecord(media){this.props.selectMedia({...media,mediaType:'unsplashRecord',query:this.state.needle});if(!this.props.multiSelect){await this.props.save();}},async submitCredentials(key,appId){this.state.unsplashError=null;await this.rpc('/web_unsplash/save_unsplash',{key,appId});await this.searchUnsplash();},});ImageSelector.components={...ImageSelector.components,UnsplashError,};patch(MediaDialog.prototype,{setup(){super.setup();this.uploadService=useService('upload');},async save(){const selectedImages=this.selectedMedia[TABS.IMAGES.id];if(selectedImages){const unsplashRecords=selectedImages.filter(media=>media.mediaType==='unsplashRecord');if(unsplashRecords.length){await this.uploadService.uploadUnsplashRecords(unsplashRecords,{resModel:this.props.resModel,resId:this.props.resId},(attachments)=>{this.selectedMedia[TABS.IMAGES.id]=this.selectedMedia[TABS.IMAGES.id].filter(media=>media.mediaType!=='unsplashRecord');this.selectedMedia[TABS.IMAGES.id]=this.selectedMedia[TABS.IMAGES.id].concat(attachments.map(attachment=>({...attachment,mediaType:'attachment'})));});}}
return super.save(...arguments);},});patch(uploadService,{start(env,{rpc}){const service=super.start(...arguments);return{...service,async uploadUnsplashRecords(records,{resModel,resId},onUploaded){service.incrementId();const file=service.addFile({id:service.fileId,name:records.length>1?_t("Uploading %s '%s' images.",records.length,records[0].query):_t("Uploading '%s' image.",records[0].query),});try{const urls={};for(const record of records){const _1920Url=new URL(record.urls.regular);_1920Url.searchParams.set('w','1920');urls[record.id]={url:_1920Url.href,download_url:record.links.download_location,description:record.alt_description,};}
const xhr=new XMLHttpRequest();xhr.upload.addEventListener('progress',ev=>{const rpcComplete=ev.loaded/ev.total*100;file.progress=rpcComplete;});xhr.upload.addEventListener('load',function(){file.progress=100;});const attachments=await rpc('/web_unsplash/attachment/add',{'res_id':resId,'res_model':resModel,'unsplashurls':urls,'query':records[0].query,},{xhr});if(attachments.error){file.hasError=true;file.errorMessage=attachments.error;}else{file.uploaded=true;await onUploaded(attachments);}
setTimeout(()=>service.deleteFile(file.id),AUTOCLOSE_DELAY);}catch(error){file.hasError=true;setTimeout(()=>service.deleteFile(file.id),AUTOCLOSE_DELAY);throw error;}}};}});return __exports;});;

/* /web_unsplash/static/src/services/unsplash_service.js */
odoo.define('@web_unsplash/services/unsplash_service',['@web/core/registry'],function(require){'use strict';let __exports={};const{registry}=require('@web/core/registry');const unsplashService=__exports.unsplashService={dependencies:['rpc'],async start(env,{rpc}){const _cache={};return{async getImages(query,offset=0,pageSize=30,orientation){const from=offset;const to=offset+pageSize;let cachedData=orientation?_cache[query+orientation]:_cache[query];if(cachedData&&(cachedData.images.length>=to||(cachedData.totalImages!==0&&cachedData.totalImages<to))){return{images:cachedData.images.slice(from,to),isMaxed:to>cachedData.totalImages};}
cachedData=await this._fetchImages(query,orientation);return{images:cachedData.images.slice(from,to),isMaxed:to>cachedData.totalImages};},async _fetchImages(query,orientation){const key=orientation?query+orientation:query;if(!_cache[key]){_cache[key]={images:[],maxPages:0,totalImages:0,pageCached:0};}
const cachedData=_cache[key];const payload={query:query,page:cachedData.pageCached+1,per_page:30,};if(orientation){payload.orientation=orientation;}
const result=await rpc('/web_unsplash/fetch_images',payload);if(result.error){return Promise.reject(result.error);}
cachedData.pageCached++;cachedData.images.push(...result.results);cachedData.maxPages=result.total_pages;cachedData.totalImages=result.total;return cachedData;},};},};registry.category('services').add('unsplash',unsplashService);return __exports;});

                    /*******************************************
                    *  Templates                               *
                    *******************************************/

                    odoo.define('web_editor.assets_media_dialog.bundle.xml', ['@web/core/registry'], function(require){
                        'use strict';
                        const { registry } = require('@web/core/registry');
                        registry.category(`xml_templates`).add(`web_editor.assets_media_dialog`, `<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
<t t-name="web_editor.HistoryDialog">
        <Dialog size="size" title="title">
            <div class="dialog-container html-history-dialog">
                <div class="revision-list d-flex flex-column align-content-stretch">
                    <t t-if="!state.revisionsData.length">
                        <div class="text-center w-100 pb-2 pt-0 px-0 fw-bolder">No history</div>
                    </t>

                    <t t-foreach="state.revisionsData" t-as="rev" t-key="rev.revision_id">
                        <a type="object" href="#" role="button" t-attf-class="btn btn-outline-primary #{state.revisionId === rev.revision_id ? 'active' : ''}" t-on-click="() =&gt; this.updateCurrentRevision(rev.revision_id )">
                            <strong><t t-esc="this.getRevisionDate(rev)"/></strong>
                            <br/>
                            <small><t t-esc="rev.create_user_name"/></small>
                        </a>
                    </t>
                </div>
                <div class="history-container o_notebook">
                    <ul class="nav nav-tabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="history-content" data-bs-toggle="tab" data-bs-target="#history-content-tab" type="button" role="tab" aria-controls="content" aria-selected="true">Content</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="history-comparison" data-bs-toggle="tab" data-bs-target="#history-comparison-tab" type="button" role="tab" aria-controls="comparison" aria-selected="false">Comparison</button>
                        </li>
                    </ul>
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="history-content-tab" role="tabpanel" aria-labelledby="history-content">
                            <t t-out="state.revisionContent"/>
                        </div>
                        <div class="tab-pane fade" id="history-comparison-tab" role="tabpanel" aria-labelledby="history-comparison">
                            <t t-out="state.revisionComparison"/>
                        </div>
                    </div>
                </div>
            </div>
            <t t-set-slot="footer">
                <button class="btn btn-primary" t-on-click="_onRestoreRevisionClick">Restore history</button>
                <button class="btn btn-secondary" t-on-click="props.close">Cancel</button>
            </t>
        </Dialog>
    </t>
<t t-name="web_editor.DocumentAttachment">
    <div class="o_existing_attachment_cell o_we_attachment_highlight card col-2 position-relative mb-2 p-2 opacity-trigger-hover cursor-pointer" t-att-class="{ o_we_attachment_selected: props.selected }" t-on-click="props.selectAttachment">
        <RemoveButton remove="() =&gt; this.remove()"/>
        <div t-att-data-url="props.url" role="img" t-att-aria-label="props.name" t-att-title="props.name" t-att-data-mimetype="props.mimetype" class="o_image d-flex align-items-center justify-content-center"/>
        <small class="o_file_name d-block text-truncate" t-esc="props.name"/>
    </div>
</t>

<t t-name="web_editor.DocumentsListTemplate">
    <div class="o_we_existing_attachments o_we_documents" t-ref="existing-attachments">
        <div t-if="!hasContent" class="o_nocontent_help">
            <p class="o_empty_folder_image">No documents found.</p>
            <p class="o_empty_folder_subtitle">You can upload documents with the button located in the top left of the screen.</p>
        </div>
        <div t-else="" class="d-flex flex-wrap gap-2">
            <t t-foreach="state.attachments" t-as="attachment" t-key="attachment.id">
                <DocumentAttachment url="attachment.url" name="attachment.name" mimetype="attachment.mimetype" id="attachment.id" onRemoved="(attachmentId) =&gt; this.onRemoved(attachmentId)" selected="this.selectedAttachmentIds.includes(attachment.id)" selectAttachment="() =&gt; this.onClickDocument(attachment)"/>
            </t>
        </div>
    </div>
</t>
<t t-name="web_editor.FileSelectorControlPanel">
    <div class="o_we_file_selector_control_panel sticky-top d-flex flex-wrap gap-2 mb-1 p-3 align-items-end">
        <SearchMedia searchPlaceholder="props.searchPlaceholder" needle="props.needle" search="props.search"/>
        <div class="d-flex gap-3 justify-content-start align-items-center">
            <div t-if="props.showOptimizedOption" class="flex-shrink-0 form-check form-switch align-items-center" t-on-change="props.changeShowOptimized">
                <input class="o_we_show_optimized form-check-input" type="checkbox" t-att-checked="props.showOptimized" id="o_we_show_optimized_switch"/>
                <label class="form-check-label" for="o_we_show_optimized_switch">
                    Show optimized images
                </label>
            </div>
            <select t-if="showSearchServiceSelect" class="o_input o_we_search_select form-select" t-on-change="ev =&gt; props.changeSearchService(ev.target.value)">
                <option t-att-selected="props.searchService === 'all'" value="all">All</option>
                <option t-att-selected="props.searchService === 'database'" value="database">My Images</option>
                <option t-if="props.useMediaLibrary" t-att-selected="props.searchService === 'media-library'" value="media-library">Illustrations</option>
        <option t-if="props.useUnsplash" t-att-selected="props.searchService === 'unsplash'" value="unsplash">Photos (via Unsplash)</option>
            </select>
        </div>
        <div class="col justify-content-end flex-nowrap input-group has-validation">
            <input type="text" class="form-control o_input o_we_url_input o_we_transition_ease flex-grow-0" t-att-class="{ o_we_horizontal_collapse: !state.showUrlInput, 'w-auto': state.showUrlInput }" name="url" t-att-placeholder="props.urlPlaceholder" t-model="state.urlInput" t-on-input="onUrlInput" t-if="state.showUrlInput"/>
            <button type="button" class="btn o_upload_media_url_button text-nowrap" t-att-class="{ 'btn-primary': state.urlInput, 'btn-secondary': !state.urlInput}" t-on-click="onUrlUploadClick" t-att-disabled="!enableUrlUploadClick">
                    <t t-esc="props.addText"/>
            </button>
            <div class="d-flex align-items-center">
                <span t-if="state.urlInput and state.isValidUrl and state.isValidFileFormat" class="o_we_url_success text-success mx-2 fa fa-lg fa-check" title="The URL seems valid."/>
                <span t-if="state.urlInput and !state.isValidUrl" class="o_we_url_error text-danger mx-2 fa fa-lg fa-times" title="The URL does not seem to work."/>
                <span t-if="props.urlWarningTitle and state.urlInput and state.isValidUrl and !state.isValidFileFormat" class="o_we_url_warning text-warning mx-2 fa fa-lg fa-warning" t-att-title="props.urlWarningTitle"/>
            </div>
        </div>
        <input type="file" class="d-none o_file_input" t-on-change="onChangeFileInput" t-ref="file-input" t-att-accept="props.accept" t-att-multiple="props.multiSelect and 'multiple'"/>
        <div class="col-auto btn-group">
            <button type="button" class="btn btn-primary o_upload_media_button" t-on-click="onClickUpload">
                <t t-esc="props.uploadText"/>
            </button>
        </div>
    </div>
</t>

<t t-name="web_editor.FileSelector">
    <div>
        <FileSelectorControlPanel uploadText="uploadText" accept="fileMimetypes" urlPlaceholder="urlPlaceholder" addText="addText" searchPlaceholder="searchPlaceholder" urlWarningTitle="urlWarningTitle" uploadUrl="(url) =&gt; this.uploadUrl(url)" uploadFiles="(files) =&gt; this.uploadFiles(files)" showOptimizedOption="showOptimizedOption" showOptimized="state.showOptimized" changeShowOptimized="showOptimized =&gt; this.state.showOptimized = !this.state.showOptimized" changeSearchService="service =&gt; this.state.searchService = service" searchService="state.searchService" needle="state.needle" search="(needle) =&gt; this.handleSearch(needle)" useMediaLibrary="props.useMediaLibrary" validateUrl="validateUrl" multiSelect="props.multiSelect" useUnsplash="state.useUnsplash"/>
        <t t-call="{{ constructor.attachmentsListTemplate }}"/>
        <div t-if="state.unsplashError" class="d-flex mt-2 unsplash_error">
            <UnsplashError title="errorTitle" subtitle="errorSubtitle" showCredentials="['key_not_found', 401].includes(state.unsplashError)" submitCredentials="(key, appId)  =&gt; this.submitCredentials(key, appId)" hasCredentialsError="state.unsplashError === 401"/>
        </div>
        <div name="load_more_attachments" class="pt-3 pb-1 text-center mx-auto o_we_load_more" t-ref="load-more-button">
            <button t-if="canLoadMore" class="btn btn-primary o_load_more" type="button" t-on-click="handleLoadMore">
                Load more...
            </button>
            <div t-elif="hasContent" class="mt-2 o_load_done_msg">
                <span><i t-esc="allLoadedText"/></span>
            </div>
        </div>
        <div t-if="this.state.canScrollAttachments" class="position-sticky d-flex align-items-center mx-auto btn btn-primary rounded-circle oi oi-chevron-down o_scroll_attachments" t-on-click="handleScrollAttachments"/>
    </div>
</t>
<t t-name="web_editor.IconSelector">
    <div>
        <div class="o_we_file_selector_control_panel sticky-top d-flex gap-2 align-items-center mb-1 py-4 px-3">
            <SearchMedia searchPlaceholder="searchPlaceholder" search.bind="this.search" needle="state.needle"/>
        </div>
        <div class="font-icons-icons">
            <t t-foreach="state.fonts" t-as="font" t-key="font.base">
                <div t-if="!font.icons.length" class="o_nocontent_help">
                    <p class="o_empty_folder_image">No pictograms found.</p>
                    <p class="o_empty_folder_subtitle">Try searching with other keywords.</p>
                </div>
                <span t-foreach="font.icons" t-as="icon" t-key="icon.id" t-att-title="icon.names[0]" t-att-aria-label="icon.names[0]" role="img" class="font-icons-icon m-2 fs-2 p-3 cursor-pointer text-center" t-att-class="{ o_we_attachment_selected: this.selectedMediaIds.includes(icon.id) }" t-attf-class="{{ font.base }} {{ icon.names[0] }}" t-on-click="() =&gt; this.onClickIcon(font, icon)"/>
            </t>
        </div>
    </div>
</t>
<t t-name="web_editor.AutoResizeImage">
    <div t-ref="auto-resize-image-container" class="o_existing_attachment_cell o_we_image align-items-center justify-content-center me-1 mb-1 opacity-trigger-hover opacity-0" t-att-class="{ o_we_attachment_optimized: props.isOptimized, 'o_loaded position-relative opacity-100': state.loaded, o_we_attachment_selected: props.selected, 'position-fixed': !state.loaded, 'cursor-pointer': !props.unselectable }" t-on-click="props.onImageClick">
        <RemoveButton t-if="props.isRemovable" model="props.model" remove="() =&gt; this.remove()"/>
        <div class="o_we_media_dialog_img_wrapper" t-att-class="{ 'bg-light': props.unselectable }">
            <t t-set="unselectable_attachment_title">You can not use this image in a field</t>
            <img t-ref="auto-resize-image" class="o_we_attachment_highlight img img-fluid w-100" t-att-class="{ 'opacity-25': props.unselectable}" t-att-src="props.src" t-att-alt="props.altDescription" loading="lazy" t-att-title="props.unselectable ? unselectable_attachment_title : props.title"/>
            <a t-if="props.author" class="o_we_media_author position-absolute start-0 bottom-0 end-0 text-truncate text-center text-primary fs-6 bg-white-50" t-att-href="props.authorLink" target="_blank" t-esc="props.author"/>
        </div>
        <span t-if="props.isOptimized" class="badge position-absolute bottom-0 end-0 m-1 text-bg-success">Optimized</span>
    </div>
</t>

<t t-name="web_editor.ExternalImage">
    <t t-if="record.mediaType == 'libraryMedia'">
        <AutoResizeImage author="record.author" src="record.thumbnail_url" authorLink="record.author_link" title="record.tooltip" altDescription="record.tooltip" minRowHeight="MIN_ROW_HEIGHT" selected="this.selectedMediaIds.includes(record.id)" onImageClick="() =&gt; this.onClickMedia(record)" onLoaded="(imgEl) =&gt; this.onImageLoaded(imgEl, record)"/>
    </t>
        <t t-elif="record.mediaType == 'unsplashRecord'">
            <AutoResizeImage src="record.url" author="record.user.name" authorLink="record.user.links.html" name="record.user.name" title="record.user.name" altDescription="record.alt_description" selected="this.selectedRecordIds.includes(record.id)" onImageClick="() =&gt; this.onClickRecord(record)" minRowHeight="MIN_ROW_HEIGHT" onLoaded="(imgEl) =&gt; this.onImageLoaded(imgEl, record)"/>
        </t>
</t>

<t t-name="web_editor.ImagesListTemplate">
    <div class="o_we_existing_attachments o_we_images d-flex flex-wrap my-0" t-ref="existing-attachments">
        <t t-if="!hasContent and !isFetching">
            <div t-if="state.needle" class="o_nocontent_help">
                <p class="o_empty_folder_image">No images found.</p>
                <p class="o_empty_folder_subtitle">Wow, it feels a bit empty in here. Upload from the button in the top right corner!</p>
            </div>
            <div t-else="" class="o_we_search_prompt">
                <h2>Discover a world of awesomeness in our copyright-free image haven. No legal drama, just nice images!</h2>
            </div>
        </t>
        <t t-else="">
            <t t-if="['all', 'database'].includes(state.searchService)">
                <t t-foreach="state.attachments" t-as="attachment" t-key="attachment.id">
                    <AutoResizeImage t-if="!attachment.original_id or state.showOptimized" id="attachment.id" isOptimized="!!attachment.original_id" isRemovable="true" onRemoved="(attachmentId) =&gt; this.onRemoved(attachmentId)" selected="this.selectedAttachmentIds.includes(attachment.id)" src="attachment.thumbnail_src or attachment.image_src" name="attachment.name" title="attachment.name" unselectable="!!attachment.unselectable" altDescription="attachment.altDescription" model="attachment.res_model" minRowHeight="MIN_ROW_HEIGHT" onImageClick="() =&gt; this.onClickAttachment(attachment)" onLoaded="(imgEl) =&gt; this.onImageLoaded(imgEl, attachment)"/>
                </t>
            </t>
            <t id="o_we_media_library_images" t-if="['all', 'unsplash', 'media-library'].includes(state.searchService)">
            <t t-foreach="combinedRecords" t-as="record" t-key="record.id">
                <t t-call="web_editor.ExternalImage"/>
            </t>
        </t>
    <t t-foreach="[...Array(20).keys()]" t-as="i" t-key="i">
                <div class="o_we_attachment_placeholder"/>
            </t>
        </t>
    </div>
</t>
<t t-name="web_editor.MediaDialog">
    <Dialog contentClass="contentClass" size="size" title="title" modalRef="modalRef">
        <Notebook pages="tabs" onPageUpdate.bind="onTabChange" defaultPage="state.activeTab"/>
        <t t-set-slot="footer">
            <button class="btn btn-primary" t-on-click="() =&gt; this.save()" t-ref="add-button">Add</button>
            <button class="btn btn-secondary" t-on-click="() =&gt; this.props.close()">Discard</button>
        </t>
    </Dialog>
</t>
<t t-name="web_editor.VideoOption">
    <div class="mb-1">
        <label class="d-flex align-items-start gap-2 cursor-pointer" t-on-change="props.onChangeOption">
            <div class="o_switch flex-shrink-0">
                <input type="checkbox" t-att-checked="props.value"/>
                <span/>
            </div>
            <span t-esc="props.label"/>
            <span t-if="props.description" class="text-muted" t-esc="props.description"/>
        </label>
    </div>
</t>

<t t-name="web_editor.VideoIframe">
    <iframe t-att-src="this.props.src" class="o_video_dialog_iframe mw-100 mh-100 overflow-hidden shadow" width="1280" height="720" allowfullscreen="allowfullscreen" frameborder="0"/>
</t>

<t t-name="web_editor.VideoSelector">
    <div class="row">
        <div class="col mt-4 o_video_dialog_form">
            <div class="mb-2">
                <label for="o_video_text">
                    <b>Video code </b>(URL or Embed)
                </label>
                <div class="text-start">
                    <small class="text-muted">Accepts <b><i>Youtube</i></b>, <b><i>Vimeo</i></b>, <b><i>Dailymotion</i></b> and <b><i>Youku</i></b> videos</small>
                </div>
                <textarea t-ref="autofocus" t-model="state.urlInput" class="form-control" id="o_video_text" placeholder="Copy-paste your URL or embed code here" t-on-input="onChangeUrl" t-att-class="{ 'is-valid': state.urlInput and !this.state.errorMessage, 'is-invalid': state.urlInput and this.state.errorMessage }"/>
            </div>
            <div t-if="shownOptions.length" class="o_video_dialog_options">
                <VideoOption t-foreach="shownOptions" t-as="option" t-key="option.id" value="option.value" onChangeOption="() =&gt; this.onChangeOption(option.id)" label="option.label" description="option.description"/>
            </div>
            <t t-if="state.vimeoPreviews.length">
                <span class="fw-bold">Suggestions</span>
                <div id="video-suggestion" class="mt-4 d-flex flex-wrap mh-75 overflow-auto">
                    <t t-foreach="state.vimeoPreviews" t-as="vimeoPreview" t-key="vimeoPreview.id">
                        <div class="o_sample_video w-25 mh-100 cursor-pointer" t-on-click="() =&gt; this.onClickSuggestion(vimeoPreview.src)">
                            <img class="mw-100 mh-100 p-1" t-att-src="vimeoPreview.thumbnailSrc"/>
                        </div>
                    </t>
                </div>
            </t>
        </div>
        <div class="col-md-6">
            <div class="o_video_preview position-relative border-0 p-3">
                <div t-if="this.state.src and !this.state.errorMessage" class="o_video_dialog_preview_text mb-2">Preview</div>
                <div class="media_iframe_video">
                    <div class="media_iframe_video_size"/>
                    <VideoIframe t-if="this.state.src and !this.state.errorMessage" src="this.state.src"/>
                    <div t-if="this.state.errorMessage" class="alert alert-warning o_video_dialog_iframe mw-100 mh-100 mb-2 mt-2" t-esc="this.state.errorMessage"/>
                </div>
            </div>
        </div>
    </div>
</t>
<t t-name="web_editor.ProgressBar">
    <small class="text-info d-flex align-items-center me-2">
        <span t-if="!props.hasError and !props.uploaded"><i class="fa fa-circle-o-notch fa-spin me-2"/></span>
        <span class="fst-italic fw-bold text-truncate flex-grow-1 me-2" t-esc="props.name"/>
        <span class="fw-bold text-nowrap" t-esc="props.size"/>
    </small>
    <small t-if="props.uploaded or props.hasError" class="d-flex align-items-center mt-1">
        <span t-if="props.uploaded" class="text-success"><i class="fa fa-check my-1 me-1"/> File has been uploaded</span>
        <span t-else="" class="text-danger"><i class="fa fa-times float-start my-1 me-1"/> <span class="o_we_error_text" t-esc="props.errorMessage ? props.errorMessage : 'File could not be saved'"/></span>
    </small>
    <div t-else="" class="progress">
        <div class="progress-bar bg-info progress-bar-striped progress-bar-animated" role="progressbar" t-attf-style="width: {{this.progress}}%;" aria-label="Progress bar"><span t-esc="this.progress + '%'"/></div>
    </div>
    <hr/>
</t>

<t t-name="web_editor.UploadProgressToast">
    <div class="o_notification_manager o_upload_progress_toast">
        <div t-if="state.isVisible" class="o_notification position-relative show fade mb-2 border border-info bg-white" role="alert" aria-live="assertive" aria-atomic="true">
            <button type="button" class="btn btn-close o_notification_close p-2" aria-label="Close" t-on-click="props.close"/>
            <div class="o_notification_body ps-2 pe-4 py-2">
                <div class="me-auto o_notification_content">
                    <div t-foreach="state.files" t-as="file" t-key="file" class="o_we_progressbar">
                        <ProgressBar progress="file_value.progress" errorMessage="file_value.errorMessage" hasError="file_value.hasError" name="file_value.name" uploaded="file_value.uploaded" size="file_value.size"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</t>
<t t-name="web_unsplash.UnsplashError">
    <div class="alert alert-info w-100">
        <h4><t t-esc="props.title"/></h4>
        <p><t t-esc="props.subtitle"/></p>
        <UnsplashCredentials t-if="props.showCredentials" submitCredentials="props.submitCredentials" hasCredentialsError="props.hasCredentialsError"/>
    </div>
</t>

<t t-name="web_unsplash.UnsplashCredentials">
    <div class="d-flex align-items-center flex-wrap">
        <a href="https://www.odoo.com/documentation/17.0/applications/websites/website/optimize/unsplash.html#generate-an-unsplash-access-key" class="me-1" target="_blank">Get an Access key</a>
        and paste it here:
        <input type="text" class="o_input o_required_modifier form-control w-auto mx-2" id="accessKeyInput" placeholder="Paste your access key here" t-model="state.key" t-on-input="() =&gt; this.state.hasKeyError = false" t-att-class="{ 'is-invalid': state.hasKeyError }"/>
        and paste
        <a href="https://www.odoo.com/documentation/17.0/applications/websites/website/optimize/unsplash.html#generate-an-unsplash-application-id" class="mx-1" target="_blank">Application ID</a>
        here:
        <input type="text" class="o_input o_required_modifier form-control w-auto ms-2" placeholder="Paste your application ID here" t-model="state.appId" t-on-input="() =&gt; this.state.hasAppIdError = false" t-att-class="{ 'is-invalid': state.hasAppIdError }"/>
        <button type="button" class="btn btn-primary w-auto ms-3 p-auto save_unsplash" t-on-click="() =&gt; this.submitCredentials()">Apply</button>
    </div>
</t>


</templates>`);
                    });