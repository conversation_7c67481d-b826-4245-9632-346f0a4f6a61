

/* ## CSS error message ##*/
body::before {
  font-weight: bold;
  content: "A css error occured, using an old style to render this page";
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 100000000000;
  background-color: #C00;
  color: #DDD;
}

css_error_message {
  content: "Error: no mixin named o-hover-text-color\A        on line 439:14 of stdin\A>>     @include o-hover-text-color(
\A   -------------^\AThis error occurred while compiling the bundle 'web._assets_primary_variables' containing:\A    - /web/static/src/scss/primary_variables.scss\A    - /web/static/src/core/avatar/avatar.variables.scss\A    - /web/static/src/core/notifications/notification.variables.scss\A    - /web/static/src/search/control_panel/control_panel.variables.scss\A    - /web/static/src/search/search_panel/search_panel.variables.scss\A    - /web/static/src/views/fields/statusbar/statusbar_field.variables.scss\A    - /web/static/src/views/form/form.variables.scss\A    - /web/static/src/views/kanban/kanban.variables.scss\A    - /web/static/src/webclient/burger_menu/burger_menu.variables.scss\A    - /web/static/src/webclient/navbar/navbar.variables.scss\A    - /web_editor/static/src/scss/web_editor.variables.scss\A    - /web_editor/static/src/scss/wysiwyg.variables.scss";
}
