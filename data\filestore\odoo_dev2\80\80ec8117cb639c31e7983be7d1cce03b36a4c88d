
/* /web/static/src/webclient/clickbot/clickbot.js */
odoo.define('@web/webclient/clickbot/clickbot',['@odoo/owl','@web/core/browser/browser'],function(require){'use strict';let __exports={};const{App}=require("@odoo/owl");const{browser}=require("@web/core/browser/browser");const MOUSE_EVENTS=["mouseover","mouseenter","mousedown","mouseup","click"];const BLACKLISTED_MENUS=["base.menu_theme_store","base.menu_third_party","event.menu_event_registration_desk","hr_attendance.menu_hr_attendance_kiosk_no_user_mode","mrp_workorder.menu_mrp_workorder_root","account.menu_action_account_bank_journal_form","pos_preparation_display.menu_point_kitchen_display_root",];const STUDIO_SYSTRAY_ICON_SELECTOR=".o_web_studio_navbar_item:not(.o_disabled) i";let isEnterprise;let appsMenusOnly=false;let calledRPC;let errorRPC;let actionCount;let env;let studioCount;let appIndex;let menuIndex;let subMenuIndex;let testedApps;let testedMenus;let testedFilters;let testedModals;function setup(){env=odoo.__WOWL_DEBUG__.root.env;env.bus.addEventListener("ACTION_MANAGER:UI-UPDATED",uiUpdate);env.bus.addEventListener("RPC:REQUEST",onRPCRequest);env.bus.addEventListener("RPC:RESPONSE",onRPCResponse);actionCount=0;calledRPC={};errorRPC=undefined;studioCount=0;testedApps=[];testedMenus=[];testedFilters=0;testedModals=0;appIndex=0;menuIndex=0;subMenuIndex=0;isEnterprise=odoo.info&&odoo.info.isEnterprise;}
function onRPCRequest({detail}){calledRPC[detail.data.id]=detail.url;}
function onRPCResponse({detail}){delete calledRPC[detail.data.id];if(detail.error){errorRPC={...detail};}}
function uiUpdate(){actionCount++;}
function cleanup(){env.bus.removeEventListener("ACTION_MANAGER:UI-UPDATED",uiUpdate);env.bus.removeEventListener("RPC:REQUEST",onRPCRequest);env.bus.removeEventListener("RPC:RESPONSE",onRPCResponse);}
async function waitForNextAnimationFrame(){await new Promise(browser.setTimeout);await new Promise((r)=>requestAnimationFrame(r));}
async function triggerClick(target,elDescription){if(target){if(elDescription){browser.console.log(`Clicking on: ${elDescription}`);}}else{throw new Error(`No element "${elDescription}" found.`);}
MOUSE_EVENTS.forEach((type)=>{const event=new MouseEvent(type,{bubbles:true,cancelable:true,view:window});target.dispatchEvent(event);});await waitForNextAnimationFrame();}
async function waitForCondition(stopCondition){const interval=25;const initialTime=30000;let timeLimit=initialTime;function hasPendingRPC(){return Object.keys(calledRPC).length>0;}
function hasScheduledTask(){let size=0;for(const app of App.apps){size+=app.scheduler.tasks.size;}
return size>0;}
function errorDialog(){if(document.querySelector(".o_error_dialog")){if(errorRPC){browser.console.error("A RPC in error was detected, maybe it's related to the error dialog : "+
JSON.stringify(errorRPC));}
throw new Error("Error dialog detected"+document.querySelector(".o_error_dialog").innerHTML);}
return false;}
while(errorDialog()||!stopCondition()||hasPendingRPC()||hasScheduledTask()){if(timeLimit<=0){let msg=`Timeout, the clicked element took more than ${
                initialTime / 1000
            } seconds to load\n`;msg+=`Waiting for:\n`;if(Object.keys(calledRPC).length>0){msg+=` * ${Object.values(calledRPC).join(", ")} RPC\n`;}
let scheduleTasks="";for(const app of App.apps){for(const task of app.scheduler.tasks){scheduleTasks+=task.node.name+",";}}
if(scheduleTasks.length>0){msg+=` * ${scheduleTasks} scheduled tasks\n`;}
if(!stopCondition()){msg+=` * stopCondition: ${stopCondition.toString()}`;}
throw new Error(msg);}
await new Promise((resolve)=>setTimeout(resolve,interval));timeLimit-=interval;}}
async function ensureHomeMenu(){const homeMenu=document.querySelector("div.o_home_menu");if(!homeMenu){let menuToggle=document.querySelector("nav.o_main_navbar > a.o_menu_toggle");if(!menuToggle){menuToggle=document.querySelector(".o_stock_barcode_menu");}
await triggerClick(menuToggle,"home menu toggle button");await waitForCondition(()=>document.querySelector("div.o_home_menu"));}}
async function ensureAppsMenu(){const appsMenu=document.querySelector(".o_navbar_apps_menu .dropdown-menu");if(!appsMenu){const toggler=document.querySelector(".o_navbar_apps_menu .dropdown-toggle");await triggerClick(toggler,"apps menu toggle button");await waitForCondition(()=>document.querySelector(".o_navbar_apps_menu .dropdown-menu"));}}
async function getNextMenu(){const menus=document.querySelectorAll(".o_menu_sections > .dropdown > .dropdown-toggle, .o_menu_sections > .dropdown-item");if(menuIndex===menus.length){menuIndex=0;return;}
let menu=menus[menuIndex];if(menu.classList.contains("dropdown-toggle")){if(!menu.nextElementSibling){await triggerClick(menu,"menu toggler");}
const dropdown=menu.nextElementSibling;if(!dropdown){menuIndex=0;return;}
const items=dropdown.querySelectorAll(".dropdown-item");menu=items[subMenuIndex];if(subMenuIndex===items.length-1){menuIndex++;subMenuIndex=0;}else{subMenuIndex++;}}else{menuIndex++;}
return menu;}
async function getNextApp(){let apps;if(isEnterprise){await ensureHomeMenu();apps=document.querySelectorAll(".o_apps .o_app");}else{await ensureAppsMenu();apps=document.querySelectorAll(".o_navbar_apps_menu .dropdown-item");}
const app=apps[appIndex];appIndex++;return app;}
async function testStudio(){const studioIcon=document.querySelector(STUDIO_SYSTRAY_ICON_SELECTOR);if(!studioIcon){return;}
await triggerClick(studioIcon,"entering studio");await waitForCondition(()=>document.querySelector(".o_in_studio"));await triggerClick(document.querySelector(".o_web_studio_leave"),"leaving studio");await waitForCondition(()=>document.querySelector(".o_main_navbar:not(.o_studio_navbar) .o_menu_toggle"));studioCount++;}
async function testFilters(){if(appsMenusOnly===true){return;}
const searchBarMenu=document.querySelector(".o_control_panel .dropdown-toggle.o_searchview_dropdown_toggler");if(!searchBarMenu){return;}
await triggerClick(searchBarMenu);const filterMenuButton=document.querySelector(".o_control_panel .o_dropdown_container.o_filter_menu");if(!filterMenuButton){return;}
const simpleFilterSel=".o_control_panel .o_filter_menu > .dropdown-item.o_menu_item:not(.o_add_custom_filter)";const dateFilterSel=".o_control_panel .o_filter_menu > .o_accordion";const filterMenuItems=document.querySelectorAll(`${simpleFilterSel},${dateFilterSel}`);browser.console.log(`Testing ${filterMenuItems.length} filters`);testedFilters+=filterMenuItems.length;for(const filter of filterMenuItems){if(filter.classList.contains("o_accordion")){await triggerClick(filter.querySelector(".o_accordion_toggle"),`filter "${filter.innerText.trim()}"`);const firstOption=filter.querySelector(".o_accordion > .o_accordion_values > .dropdown-item");if(firstOption){await triggerClick(firstOption,`filter option "${firstOption.innerText.trim()}"`);await waitForCondition(()=>true);}}else{await triggerClick(filter,`filter "${filter.innerText.trim()}"`);await waitForCondition(()=>true);}}}
async function testViews(){if(appsMenusOnly===true){return;}
const switchButtons=document.querySelectorAll("nav.o_cp_switch_buttons > button.o_switch_view:not(.active):not(.o_map)");for(const switchButton of switchButtons){const viewType=[...switchButton.classList].find((cls)=>cls!=="o_switch_view"&&cls.startsWith("o_")).slice(2);browser.console.log(`Testing view switch: ${viewType}`);browser.setTimeout(function(){const target=document.querySelector(`nav.o_cp_switch_buttons > button.o_switch_view.o_${viewType}`);if(target){triggerClick(target,`${viewType} view switcher`);}},250);await waitForCondition(()=>{return document.querySelector(`.o_switch_view.o_${viewType}.active`)!==null;});await testStudio();await testFilters();}}
async function testMenuItem(element){const menu=element.dataset.menuXmlid;const menuDescription=element.innerText.trim()+" "+menu;if(BLACKLISTED_MENUS.includes(menu)){browser.console.log(`Skipping blacklisted menu ${menuDescription}`);return Promise.resolve();}
browser.console.log(`Testing menu ${menuDescription}`);testedMenus.push(menu);const startActionCount=actionCount;await triggerClick(element,`menu item "${element.innerText.trim()}"`);try{let isModal=false;await waitForCondition(()=>{if(document.querySelector(".o_dialog:not(.o_error_dialog)")){isModal=true;browser.console.log(`Modal detected: ${menuDescription}`);testedModals++;return true;}else{return startActionCount!==actionCount;}});if(isModal){await triggerClick(document.querySelector(".o_dialog header > .btn-close"),"modal close button");}else{await testStudio();await testFilters();await testViews();}}catch(err){browser.console.error(`Error while testing ${menuDescription}`);throw err;}}
async function testApp(element){browser.console.log(`Testing app menu: ${element.dataset.menuXmlid}`);testedApps.push(element.dataset.menuXmlid);await testMenuItem(element);if(appsMenusOnly===true){return;}
menuIndex=0;subMenuIndex=0;let menu=await getNextMenu();while(menu){await testMenuItem(menu);menu=await getNextMenu();}}
async function _clickEverywhere(xmlId){setup();console.log("Starting ClickEverywhere test");console.log(`Odoo flavor: ${isEnterprise ? "Enterprise" : "Community"}`);const startTime=performance.now();try{let app;if(xmlId){if(isEnterprise){app=document.querySelector(`a.o_app.o_menuitem[data-menu-xmlid="${xmlId}"]`);}else{await triggerClick(document.querySelector(".o_navbar_apps_menu .dropdown-toggle"));app=document.querySelector(`.o_navbar_apps_menu .dropdown-item[data-menu-xmlid="${xmlId}"]`);}
if(!app){throw new Error(`No app found for xmlid ${xmlId}`);}
await testApp(app);}else{while((app=await getNextApp())){await testApp(app);}}
console.log(`Test took ${(performance.now() - startTime) / 1000} seconds`);browser.console.log(`Successfully tested ${testedApps.length} apps`);browser.console.log(`Successfully tested ${testedMenus.length - testedApps.length} menus`);browser.console.log(`Successfully tested ${testedModals} modals`);browser.console.log(`Successfully tested ${testedFilters} filters`);if(studioCount>0){browser.console.log(`Successfully tested ${studioCount} views in Studio`);}
browser.console.log("test successful");}catch(err){console.log(`Test took ${(performance.now() - startTime) / 1000} seconds`);browser.console.error(err||"test failed");}finally{cleanup();}}
function clickEverywhere(xmlId,light){appsMenusOnly=light;browser.setTimeout(_clickEverywhere,1000,xmlId);}
window.clickEverywhere=clickEverywhere;return __exports;});