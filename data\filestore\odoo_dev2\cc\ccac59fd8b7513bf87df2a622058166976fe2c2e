

/* ## CSS error message ##*/
body::before {
  font-weight: bold;
  content: "A css error occured, using an old style to render this page";
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 100000000000;
  background-color: #C00;
  color: #DDD;
}

css_error_message {
  content: "Error: Undefined variable: \"$o-color-palette\".\A        on line 8:24 of stdin\A>> @each $name, $color in $o-color-palette {
\A   -----------------------^\AThis error occurred while compiling the bundle 'web._assets_frontend_helpers' containing:\A    - /web_editor/static/src/scss/bootstrap_overridden.scss\A    - /web/static/src/scss/bootstrap_overridden_frontend.scss";
}
